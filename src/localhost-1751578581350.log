react refresh:37 Download the React DevTools for a better development experience: https://react.dev/link/react-devtools
Player.js:187  Unknown event handler property `onReady`. It will be ignored.
validateProperty @ react-dom-client.development.js:2941
warnUnknownProperties @ react-dom-client.development.js:3153
validatePropertiesInDevelopment @ react-dom-client.development.js:17115
setInitialProperties @ react-dom-client.development.js:17741
completeWork @ react-dom-client.development.js:11390
runWithFiberInDEV @ react-dom-client.development.js:1518
completeUnitOfWork @ react-dom-client.development.js:15268
performUnitOfWork @ react-dom-client.development.js:15148
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<video>
(匿名) @ react.development.js:1025
(匿名) @ HtmlPlayer.js:5
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ForwardRef>
(匿名) @ react.development.js:1025
Player @ Player.js:59
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react.development.js:1025
renderActivePlayer @ ReactPlayer.js:60
ReactPlayer @ ReactPlayer.js:76
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ReactPlayer>
(匿名) @ react-jsx-dev-runtime.development.js:336
Player @ Player.js:187
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react-jsx-dev-runtime.development.js:336
MediaPlayerPage @ MediaPlayerPage.js:78
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10446
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<...>
(匿名) @ react-jsx-dev-runtime.development.js:336
renderTabContent @ MainLayout.js:59
(匿名) @ MainLayout.js:66
(匿名) @ MainLayout.js:62
updateMemo @ react-dom-client.development.js:6621
useMemo @ react-dom-client.development.js:23182
(匿名) @ react.development.js:1209
MainLayout @ MainLayout.js:62
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performSyncWorkOnRoot @ react-dom-client.development.js:16231
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16068
processRootScheduleInMicrotask @ react-dom-client.development.js:16116
(匿名) @ react-dom-client.development.js:16245
<MainLayout>
(匿名) @ react-jsx-dev-runtime.development.js:336
App @ App.js:6
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<App>
(匿名) @ react-jsx-dev-runtime.development.js:336
./src/index.js @ index.js:7
(匿名) @ react refresh:37
__webpack_require__ @ bootstrap:22
(匿名) @ startup:7
(匿名) @ startup:7
Player.js:187  Unknown event handler property `onStart`. It will be ignored.
validateProperty @ react-dom-client.development.js:2941
warnUnknownProperties @ react-dom-client.development.js:3153
validatePropertiesInDevelopment @ react-dom-client.development.js:17115
setInitialProperties @ react-dom-client.development.js:17741
completeWork @ react-dom-client.development.js:11390
runWithFiberInDEV @ react-dom-client.development.js:1518
completeUnitOfWork @ react-dom-client.development.js:15268
performUnitOfWork @ react-dom-client.development.js:15148
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<video>
(匿名) @ react.development.js:1025
(匿名) @ HtmlPlayer.js:5
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ForwardRef>
(匿名) @ react.development.js:1025
Player @ Player.js:59
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react.development.js:1025
renderActivePlayer @ ReactPlayer.js:60
ReactPlayer @ ReactPlayer.js:76
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ReactPlayer>
(匿名) @ react-jsx-dev-runtime.development.js:336
Player @ Player.js:187
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react-jsx-dev-runtime.development.js:336
MediaPlayerPage @ MediaPlayerPage.js:78
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10446
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<...>
(匿名) @ react-jsx-dev-runtime.development.js:336
renderTabContent @ MainLayout.js:59
(匿名) @ MainLayout.js:66
(匿名) @ MainLayout.js:62
updateMemo @ react-dom-client.development.js:6621
useMemo @ react-dom-client.development.js:23182
(匿名) @ react.development.js:1209
MainLayout @ MainLayout.js:62
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performSyncWorkOnRoot @ react-dom-client.development.js:16231
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16068
processRootScheduleInMicrotask @ react-dom-client.development.js:16116
(匿名) @ react-dom-client.development.js:16245
<MainLayout>
(匿名) @ react-jsx-dev-runtime.development.js:336
App @ App.js:6
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<App>
(匿名) @ react-jsx-dev-runtime.development.js:336
./src/index.js @ index.js:7
(匿名) @ react refresh:37
__webpack_require__ @ bootstrap:22
(匿名) @ startup:7
(匿名) @ startup:7
Player.js:187  Unknown event handler property `onDuration`. It will be ignored.
validateProperty @ react-dom-client.development.js:2941
warnUnknownProperties @ react-dom-client.development.js:3153
validatePropertiesInDevelopment @ react-dom-client.development.js:17115
setInitialProperties @ react-dom-client.development.js:17741
completeWork @ react-dom-client.development.js:11390
runWithFiberInDEV @ react-dom-client.development.js:1518
completeUnitOfWork @ react-dom-client.development.js:15268
performUnitOfWork @ react-dom-client.development.js:15148
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<video>
(匿名) @ react.development.js:1025
(匿名) @ HtmlPlayer.js:5
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ForwardRef>
(匿名) @ react.development.js:1025
Player @ Player.js:59
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react.development.js:1025
renderActivePlayer @ ReactPlayer.js:60
ReactPlayer @ ReactPlayer.js:76
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ReactPlayer>
(匿名) @ react-jsx-dev-runtime.development.js:336
Player @ Player.js:187
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react-jsx-dev-runtime.development.js:336
MediaPlayerPage @ MediaPlayerPage.js:78
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10446
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<...>
(匿名) @ react-jsx-dev-runtime.development.js:336
renderTabContent @ MainLayout.js:59
(匿名) @ MainLayout.js:66
(匿名) @ MainLayout.js:62
updateMemo @ react-dom-client.development.js:6621
useMemo @ react-dom-client.development.js:23182
(匿名) @ react.development.js:1209
MainLayout @ MainLayout.js:62
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performSyncWorkOnRoot @ react-dom-client.development.js:16231
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16068
processRootScheduleInMicrotask @ react-dom-client.development.js:16116
(匿名) @ react-dom-client.development.js:16245
<MainLayout>
(匿名) @ react-jsx-dev-runtime.development.js:336
App @ App.js:6
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<App>
(匿名) @ react-jsx-dev-runtime.development.js:336
./src/index.js @ index.js:7
(匿名) @ react refresh:37
__webpack_require__ @ bootstrap:22
(匿名) @ startup:7
(匿名) @ startup:7
Player.js:187  Unknown event handler property `onBuffer`. It will be ignored.
validateProperty @ react-dom-client.development.js:2941
warnUnknownProperties @ react-dom-client.development.js:3153
validatePropertiesInDevelopment @ react-dom-client.development.js:17115
setInitialProperties @ react-dom-client.development.js:17741
completeWork @ react-dom-client.development.js:11390
runWithFiberInDEV @ react-dom-client.development.js:1518
completeUnitOfWork @ react-dom-client.development.js:15268
performUnitOfWork @ react-dom-client.development.js:15148
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<video>
(匿名) @ react.development.js:1025
(匿名) @ HtmlPlayer.js:5
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ForwardRef>
(匿名) @ react.development.js:1025
Player @ Player.js:59
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react.development.js:1025
renderActivePlayer @ ReactPlayer.js:60
ReactPlayer @ ReactPlayer.js:76
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ReactPlayer>
(匿名) @ react-jsx-dev-runtime.development.js:336
Player @ Player.js:187
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react-jsx-dev-runtime.development.js:336
MediaPlayerPage @ MediaPlayerPage.js:78
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10446
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<...>
(匿名) @ react-jsx-dev-runtime.development.js:336
renderTabContent @ MainLayout.js:59
(匿名) @ MainLayout.js:66
(匿名) @ MainLayout.js:62
updateMemo @ react-dom-client.development.js:6621
useMemo @ react-dom-client.development.js:23182
(匿名) @ react.development.js:1209
MainLayout @ MainLayout.js:62
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performSyncWorkOnRoot @ react-dom-client.development.js:16231
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16068
processRootScheduleInMicrotask @ react-dom-client.development.js:16116
(匿名) @ react-dom-client.development.js:16245
<MainLayout>
(匿名) @ react-jsx-dev-runtime.development.js:336
App @ App.js:6
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<App>
(匿名) @ react-jsx-dev-runtime.development.js:336
./src/index.js @ index.js:7
(匿名) @ react refresh:37
__webpack_require__ @ bootstrap:22
(匿名) @ startup:7
(匿名) @ startup:7
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
Player.js:187  Unknown event handler property `onBufferEnd`. It will be ignored.
validateProperty @ react-dom-client.development.js:2941
warnUnknownProperties @ react-dom-client.development.js:3153
validatePropertiesInDevelopment @ react-dom-client.development.js:17115
setInitialProperties @ react-dom-client.development.js:17741
completeWork @ react-dom-client.development.js:11390
runWithFiberInDEV @ react-dom-client.development.js:1518
completeUnitOfWork @ react-dom-client.development.js:15268
performUnitOfWork @ react-dom-client.development.js:15148
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<video>
(匿名) @ react.development.js:1025
(匿名) @ HtmlPlayer.js:5
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ForwardRef>
(匿名) @ react.development.js:1025
Player @ Player.js:59
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react.development.js:1025
renderActivePlayer @ ReactPlayer.js:60
ReactPlayer @ ReactPlayer.js:76
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ReactPlayer>
(匿名) @ react-jsx-dev-runtime.development.js:336
Player @ Player.js:187
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react-jsx-dev-runtime.development.js:336
MediaPlayerPage @ MediaPlayerPage.js:78
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10446
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<...>
(匿名) @ react-jsx-dev-runtime.development.js:336
renderTabContent @ MainLayout.js:59
(匿名) @ MainLayout.js:66
(匿名) @ MainLayout.js:62
updateMemo @ react-dom-client.development.js:6621
useMemo @ react-dom-client.development.js:23182
(匿名) @ react.development.js:1209
MainLayout @ MainLayout.js:62
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performSyncWorkOnRoot @ react-dom-client.development.js:16231
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16068
processRootScheduleInMicrotask @ react-dom-client.development.js:16116
(匿名) @ react-dom-client.development.js:16245
<MainLayout>
(匿名) @ react-jsx-dev-runtime.development.js:336
App @ App.js:6
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<App>
(匿名) @ react-jsx-dev-runtime.development.js:336
./src/index.js @ index.js:7
(匿名) @ react refresh:37
__webpack_require__ @ bootstrap:22
(匿名) @ startup:7
(匿名) @ startup:7
Player.js:187  Unknown event handler property `onSeek`. It will be ignored.
validateProperty @ react-dom-client.development.js:2941
warnUnknownProperties @ react-dom-client.development.js:3153
validatePropertiesInDevelopment @ react-dom-client.development.js:17115
setInitialProperties @ react-dom-client.development.js:17741
completeWork @ react-dom-client.development.js:11390
runWithFiberInDEV @ react-dom-client.development.js:1518
completeUnitOfWork @ react-dom-client.development.js:15268
performUnitOfWork @ react-dom-client.development.js:15148
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<video>
(匿名) @ react.development.js:1025
(匿名) @ HtmlPlayer.js:5
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ForwardRef>
(匿名) @ react.development.js:1025
Player @ Player.js:59
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react.development.js:1025
renderActivePlayer @ ReactPlayer.js:60
ReactPlayer @ ReactPlayer.js:76
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateForwardRef @ react-dom-client.development.js:8645
beginWork @ react-dom-client.development.js:10861
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<ReactPlayer>
(匿名) @ react-jsx-dev-runtime.development.js:336
Player @ Player.js:187
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<Player>
(匿名) @ react-jsx-dev-runtime.development.js:336
MediaPlayerPage @ MediaPlayerPage.js:78
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10446
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopConcurrentByScheduler @ react-dom-client.development.js:15125
renderRootConcurrent @ react-dom-client.development.js:15099
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<...>
(匿名) @ react-jsx-dev-runtime.development.js:336
renderTabContent @ MainLayout.js:59
(匿名) @ MainLayout.js:66
(匿名) @ MainLayout.js:62
updateMemo @ react-dom-client.development.js:6621
useMemo @ react-dom-client.development.js:23182
(匿名) @ react.development.js:1209
MainLayout @ MainLayout.js:62
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooks @ react-dom-client.development.js:5529
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performSyncWorkOnRoot @ react-dom-client.development.js:16231
flushSyncWorkAcrossRoots_impl @ react-dom-client.development.js:16068
processRootScheduleInMicrotask @ react-dom-client.development.js:16116
(匿名) @ react-dom-client.development.js:16245
<MainLayout>
(匿名) @ react-jsx-dev-runtime.development.js:336
App @ App.js:6
react-stack-bottom-frame @ react-dom-client.development.js:23863
renderWithHooksAgain @ react-dom-client.development.js:5629
renderWithHooks @ react-dom-client.development.js:5541
updateFunctionComponent @ react-dom-client.development.js:8897
beginWork @ react-dom-client.development.js:10522
runWithFiberInDEV @ react-dom-client.development.js:1518
performUnitOfWork @ react-dom-client.development.js:15130
workLoopSync @ react-dom-client.development.js:14956
renderRootSync @ react-dom-client.development.js:14936
performWorkOnRoot @ react-dom-client.development.js:14417
performWorkOnRootViaSchedulerTask @ react-dom-client.development.js:16216
performWorkUntilDeadline @ scheduler.development.js:45
<App>
(匿名) @ react-jsx-dev-runtime.development.js:336
./src/index.js @ index.js:7
(匿名) @ react refresh:37
__webpack_require__ @ bootstrap:22
(匿名) @ startup:7
(匿名) @ startup:7
Player.js:54 === 媒体源改变 ===
Player.js:55 新媒体URL: https://videocdn.cdnpk.net/videos/8c8ac42c-69e4-4616-9080-eabaf3ba9f95/horizontal/previews/videvo_watermarked/large.mp4
Player.js:56 媒体类型: video
Player.js:57 媒体标题: MP4介绍视频
Player.js:61 ReactPlayer.canPlay(): true
Player.js:39 🔄 重置播放器状态
Player.js:107 🚀 Player组件挂载，初始化状态
Player.js:54 === 媒体源改变 ===
Player.js:55 新媒体URL: https://videocdn.cdnpk.net/videos/8c8ac42c-69e4-4616-9080-eabaf3ba9f95/horizontal/previews/videvo_watermarked/large.mp4
Player.js:56 媒体类型: video
Player.js:57 媒体标题: MP4介绍视频
Player.js:61 ReactPlayer.canPlay(): true
Player.js:39 🔄 重置播放器状态
Player.js:107 🚀 Player组件挂载，初始化状态
Player.js:54 === 媒体源改变 ===
Player.js:55 新媒体URL: https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4
Player.js:56 媒体类型: video
Player.js:57 媒体标题: Big Buck Bunny
Player.js:61 ReactPlayer.canPlay(): true
Player.js:39 🔄 重置播放器状态
Player.js:126 播放状态切换: false -> true
Player.js:88 播放器状态变化: playing = true
