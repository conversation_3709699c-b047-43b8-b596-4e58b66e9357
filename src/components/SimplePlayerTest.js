import React from 'react';
import ReactPlayer from 'react-player';

function SimplePlayerTest() {
  return (
    <div style={{ padding: '20px' }}>
      <h2>简单ReactPlayer测试</h2>
      <div style={{ width: '640px', height: '360px', margin: '20px 0' }}>
        {/* 按照您的示例 */}
        <ReactPlayer 
          src='https://www.youtube.com/watch?v=LXb3EKWsInQ' 
          width="100%"
          height="100%"
          controls
        />
      </div>
      
      <div style={{ width: '640px', height: '360px', margin: '20px 0' }}>
        {/* 测试MP4视频 */}
        <ReactPlayer 
          src='https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4' 
          width="100%"
          height="100%"
          controls
        />
      </div>
    </div>
  );
}

export default SimplePlayerTest;
