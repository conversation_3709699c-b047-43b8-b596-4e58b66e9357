import React from 'react';
import Icon from './Icon';

const Tabs = ({ items, activeKey, onChange, onEdit }) => {
  return (
    <div className="tabs-container">
      <ul className="tabs-nav">
        {items.map(item => (
          <li
            key={item.key}
            className={`tabs-tab ${item.key === activeKey ? 'active' : ''}`}
            onClick={() => onChange(item.key)}
          >
            <span>{item.label}</span>
            {item.closable && (
              <Icon
                name="close"
                className="tab-close-btn"
                style={{ fontSize: 14, marginLeft: 8, cursor: 'pointer' }}
                onClick={(e) => {
                  e.stopPropagation();
                  onEdit(item.key, 'remove');
                }}
              />
            )}
          </li>
        ))}
      </ul>
      <div className="tabs-content">
        {items.map(item => (
          <div
            key={item.key}
            className="tab-pane"
            style={{ display: item.key === activeKey ? 'block' : 'none' }}
          >
            {item.children}
          </div>
        ))}
      </div>
    </div>
  );
};

export default Tabs;
