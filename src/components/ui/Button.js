import React from 'react';

const Button = ({ children, onClick, className = '', type = 'default', shape, icon, disabled, loading }) => {
  const classNames = `custom-button ${type} ${shape ? shape : ''} ${className}`;
  return (
    <button className={classNames} onClick={onClick} disabled={disabled || loading}>
      {loading && <div className="spinner-small" />}
      {icon}
      {children}
    </button>
  );
};

export default Button;
