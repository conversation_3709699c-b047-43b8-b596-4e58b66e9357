import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Tabs from '../Tabs';

describe('Tabs Component', () => {
  const mockItems = [
    {
      key: '1',
      label: '关键词洞察',
      closable: true,
      children: <div>Content 1</div>
    },
    {
      key: '2',
      label: 'Mux播放器测试',
      closable: true,
      children: <div>Content 2</div>
    },
    {
      key: '3',
      label: '不可关闭页签',
      closable: false,
      children: <div>Content 3</div>
    }
  ];

  const defaultProps = {
    items: mockItems,
    activeKey: '1',
    onChange: jest.fn(),
    onEdit: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders all tab items', () => {
    render(<Tabs {...defaultProps} />);
    
    expect(screen.getByText('关键词洞察')).toBeInTheDocument();
    expect(screen.getByText('Mux播放器测试')).toBeInTheDocument();
    expect(screen.getByText('不可关闭页签')).toBeInTheDocument();
  });

  test('shows active tab content', () => {
    render(<Tabs {...defaultProps} />);
    
    expect(screen.getByText('Content 1')).toBeInTheDocument();
    expect(screen.queryByText('Content 2')).not.toBeVisible();
  });

  test('calls onChange when tab is clicked', () => {
    render(<Tabs {...defaultProps} />);
    
    fireEvent.click(screen.getByText('Mux播放器测试'));
    expect(defaultProps.onChange).toHaveBeenCalledWith('2');
  });

  test('renders close buttons for closable tabs', () => {
    const { container } = render(<Tabs {...defaultProps} />);

    // 应该有2个关闭按钮（对应2个可关闭的页签）
    const closeButtons = container.querySelectorAll('.tab-close-btn');
    expect(closeButtons).toHaveLength(2);
  });

  test('close buttons have correct CSS class', () => {
    const { container } = render(<Tabs {...defaultProps} />);

    const closeButtons = container.querySelectorAll('.tab-close-btn');
    closeButtons.forEach(button => {
      expect(button).toHaveClass('tab-close-btn');
    });
  });

  test('calls onEdit when close button is clicked', () => {
    const { container } = render(<Tabs {...defaultProps} />);

    const closeButtons = container.querySelectorAll('.tab-close-btn');
    fireEvent.click(closeButtons[0]);

    expect(defaultProps.onEdit).toHaveBeenCalledWith('1', 'remove');
  });

  test('close button click does not trigger tab change', () => {
    const { container } = render(<Tabs {...defaultProps} />);

    const closeButtons = container.querySelectorAll('.tab-close-btn');
    fireEvent.click(closeButtons[0]);

    // onEdit应该被调用，但onChange不应该被调用
    expect(defaultProps.onEdit).toHaveBeenCalled();
    expect(defaultProps.onChange).not.toHaveBeenCalled();
  });

  test('applies active class to active tab', () => {
    render(<Tabs {...defaultProps} />);
    
    const activeTab = screen.getByText('关键词洞察').closest('li');
    expect(activeTab).toHaveClass('active');
  });

  test('non-closable tabs do not have close buttons', () => {
    render(<Tabs {...defaultProps} />);
    
    const nonClosableTab = screen.getByText('不可关闭页签').closest('li');
    const closeButtonInTab = nonClosableTab.querySelector('.tab-close-btn');
    expect(closeButtonInTab).toBeNull();
  });
});
