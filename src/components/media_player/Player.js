import React, { useState, useRef, useCallback, useEffect } from 'react';
import ReactPlayer from 'react-player';
import screenfull from 'screenfull';
import PlayerControls from './PlayerControls';
import Button from '../ui/Button';
import Icon from '../ui/Icon';
import Modal from '../ui/Modal';

import { analyzeVideoWithGemini } from '../../api/gemini';

const Player = ({ media, collections, onAddToCollection }) => {
  // 采用示例代码的完整状态结构
  const initialState = {
    playing: false,
    controls: false,
    light: false,
    volume: 0.8,
    muted: false,
    played: 0,
    loaded: 0,
    duration: 0,
    playbackRate: 1.0,
    loop: false,
    seeking: false,
    loadedSeconds: 0,
    playedSeconds: 0,
    pip: false
  };

  const [state, setState] = useState(initialState);

  // 字幕相关状态
  const [subtitles, setSubtitles] = useState({
    enabled: false,
    tracks: [],
    currentTrack: null
  });

  // 简化的状态管理
  const [isCollectionModalOpen, setIsCollectionModalOpen] = useState(false);
  const [selectedCollection, setSelectedCollection] = useState('');
  const [isAnalysisLoading, setIsAnalysisLoading] = useState(false);
  const [analysisResult, setAnalysisResult] = useState(null);
  const [isAnalysisModalOpen, setIsAnalysisModalOpen] = useState(false);

  const playerRef = useRef(null);
  const playerWrapperRef = useRef(null);

  // 采用示例代码的ref设置方式
  const setPlayerRef = useCallback((player) => {
    if (!player) return;
    playerRef.current = player;
    console.log('Player ref set:', player);
  }, []);

  // 字幕控制函数
  const toggleSubtitles = () => {
    setSubtitles(prev => ({ ...prev, enabled: !prev.enabled }));
  };

  const selectSubtitleTrack = (trackIndex) => {
    setSubtitles(prev => ({ ...prev, currentTrack: trackIndex }));
  };
  // 媒体源改变时重置状态
  useEffect(() => {
    if (media?.url) {
      console.log('媒体源改变:', media.title);
      setState(prevState => ({
        ...initialState,
        volume: prevState.volume // 保持音量设置
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [media?.url]);

  // 采用示例代码的简洁事件处理方式
  const handlePlayPause = () => {
    setState(prevState => ({ ...prevState, playing: !prevState.playing }));
  };

  const handlePlay = () => {
    console.log('onPlay');
    setState(prevState => ({ ...prevState, playing: true }));
  };

  const handlePause = () => {
    console.log('onPause');
    setState(prevState => ({ ...prevState, playing: false }));
  };

  const handleVolumeChange = (value) => {
    const volume = typeof value === 'number' ? value : parseFloat(value.target?.value || value);
    setState(prevState => ({ ...prevState, volume }));
  };

  const handleToggleMuted = () => {
    setState(prevState => ({ ...prevState, muted: !prevState.muted }));
  };

  const handleSetPlaybackRate = (rate) => {
    setState(prevState => ({ ...prevState, playbackRate: rate }));
  };

  const handleSeekMouseDown = () => {
    setState(prevState => ({ ...prevState, seeking: true }));
  };

  const handleSeekChange = (value) => {
    const played = typeof value === 'number' ? value : parseFloat(value.target?.value || value);
    setState(prevState => ({ ...prevState, played }));
  };

  const handleSeekMouseUp = (value) => {
    const played = typeof value === 'number' ? value : parseFloat(value.target?.value || value);
    setState(prevState => ({ ...prevState, seeking: false }));
    if (playerRef.current) {
      playerRef.current.seekTo(played);
    }
  };

  // 采用示例代码的progress处理方式
  const handleProgress = (progress) => {
    // 只在非seeking状态下更新进度
    if (!state.seeking) {
      setState(prevState => ({
        ...prevState,
        played: progress.played,
        loaded: progress.loaded,
        playedSeconds: progress.playedSeconds,
        loadedSeconds: progress.loadedSeconds
      }));
    }
  };

  const handleDuration = (duration) => {
    console.log('onDuration', duration);
    setState(prevState => ({ ...prevState, duration }));
  };

  const handleEnded = () => {
    console.log('onEnded');
    setState(prevState => ({ ...prevState, playing: prevState.loop }));
  };

  const handleToggleFullScreen = useCallback(() => screenfull.isEnabled && screenfull.toggle(playerWrapperRef.current), []);

  const handleAiAnalysis = async () => {
    setIsAnalysisLoading(true);
    const result = await analyzeVideoWithGemini(media);
    if (result) {
      setAnalysisResult(result);
      setIsAnalysisModalOpen(true);
    }
    setIsAnalysisLoading(false);
  };
  
  const handleAddToCollection = () => {
      if(!selectedCollection) { alert("请选择收藏夹"); return; }
      onAddToCollection(selectedCollection, media);
      setIsCollectionModalOpen(false);
      alert(`已添加`);
  }



  // 条件渲染必须在所有hooks之后
  if (!media) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%', background: '#000', color: 'white' }}>
        请从左侧选择一个媒体文件进行播放
      </div>
    );
  }

  // 检查是否为音频文件
  const isAudio = media?.type === 'audio' || (media?.url && (
    media.url.includes('.mp3') ||
    media.url.includes('.wav') ||
    media.url.includes('.ogg') ||
    media.url.includes('.m4a')
  ));

  return (
    <div ref={playerWrapperRef} className="player-wrapper">
      {/* 音频文件的可视化界面 */}
      {isAudio && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          zIndex: 1
        }}>
          <div style={{ textAlign: 'center', marginBottom: '40px' }}>
            <div style={{ fontSize: '64px', marginBottom: '20px' }}>🎵</div>
            <h2 style={{ margin: '0 0 10px 0', fontSize: '24px' }}>{media.title}</h2>
            <p style={{ margin: 0, fontSize: '16px', opacity: 0.8 }}>{media.artist || '未知艺术家'}</p>
          </div>

          {/* 简单的音频可视化效果 */}
          <div style={{ display: 'flex', alignItems: 'end', gap: '4px', height: '60px' }}>
            {[...Array(20)].map((_, i) => (
              <div
                key={i}
                style={{
                  width: '6px',
                  height: `${20 + Math.sin((Date.now() / 200) + i) * 20}px`,
                  background: 'rgba(255,255,255,0.7)',
                  borderRadius: '3px',
                  animation: state.playing ? `audioBar${i % 3} 0.8s ease-in-out infinite alternate` : 'none'
                }}
              />
            ))}
          </div>
        </div>
      )}

      <ReactPlayer
        ref={setPlayerRef}
        className="react-player"
        src={media.url}
        width="100%"
        height="100%"
        playing={state.playing}
        volume={state.volume}
        muted={state.muted}
        playbackRate={state.playbackRate}
        loop={state.loop}
        controls={false}
        light={state.light}
        pip={state.pip}
        config={{
          file: {
            attributes: {
              crossOrigin: 'anonymous',
              playsInline: true
            },
            tracks: subtitles.tracks || []
          },
          youtube: {
            color: 'blue',
            playerVars: {
              controls: 1,  // 禁用YouTube内置控制条
              modestbranding: 1,
              rel: 0
            }
          }
        }}
        onLoadStart={() => console.log('onLoadStart')}
        onReady={() => console.log('onReady')}
        onStart={(e) => console.log('onStart', e)}
        onPlay={handlePlay}
        onPause={handlePause}
        onProgress={handleProgress}
        onDuration={handleDuration}
        onEnded={handleEnded}
        onError={(e) => console.log('onError', e)}
      />
      <PlayerControls
        {...state}
        onPlayPause={handlePlayPause}
        onVolumeChange={handleVolumeChange}
        onMute={handleToggleMuted}
        onPlaybackRateChange={handleSetPlaybackRate}
        onSeek={handleSeekChange}
        onSeekMouseDown={handleSeekMouseDown}
        onSeekMouseUp={handleSeekMouseUp}
        onToggleFullScreen={handleToggleFullScreen}
        // 字幕相关属性
        subtitles={subtitles}
        onToggleSubtitles={toggleSubtitles}
        onSelectSubtitleTrack={selectSubtitleTrack}
      />
      <div style={{ position: 'absolute', top: 20, right: 20, display: 'flex', flexDirection: 'column', gap: 10 }}>
        <Button type="primary" shape="circle" icon={<Icon name="heart" />} onClick={() => setIsCollectionModalOpen(true)} />
        <Button type="primary" shape="circle" icon={<Icon name="robot" />} onClick={handleAiAnalysis} disabled={isAnalysisLoading} />
        <Button
          type="primary"
          shape="circle"
          onClick={() => {
            console.log('🔄 重新加载播放器');
            window.location.reload();
          }}
          title="重新加载播放器"
          style={{ fontSize: '12px', padding: '4px' }}
        >
          🔄
        </Button>
      </div>
      {isAnalysisLoading && <div style={{ position: 'absolute', top: '50%', left: '50%', transform: 'translate(-50%, -50%)', color: 'white' }}>分析中...</div>}




      
      <Modal isOpen={isCollectionModalOpen} onClose={() => setIsCollectionModalOpen(false)} title="添加到收藏夹">
          <p>将媒体: <strong>{media.title}</strong> 添加到</p>
          <select value={selectedCollection} onChange={(e) => setSelectedCollection(e.target.value)} style={{width: '100%', padding: 8}}>
              <option value="">请选择</option>
              {Object.entries(collections).map(([id, { name }]) => (
                <option key={id} value={id}>{name}</option>
              ))}
          </select>
          <div style={{marginTop: 16, textAlign: 'right'}}>
              <Button onClick={handleAddToCollection} type="primary">确认</Button>
          </div>
      </Modal>

      <Modal isOpen={isAnalysisModalOpen} onClose={() => setIsAnalysisModalOpen(false)} title="AI 分析结果">
        {analysisResult && (
          <div>
            <h4>内容摘要</h4>
            <p>{analysisResult.summary}</p>
            <h4>关键要点</h4>
            <ul>{analysisResult.takeaways.map((item, i) => <li key={i}>{item}</li>)}</ul>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Player;
