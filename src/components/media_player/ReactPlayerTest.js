import React, { useState } from 'react';
import ReactPlayerAdvanced from './ReactPlayerAdvanced';
import Button from '../ui/Button';
import Icon from '../ui/Icon';

const ReactPlayerTest = () => {
  const [currentMedia, setCurrentMedia] = useState(null);
  const [currentTheme, setCurrentTheme] = useState('default');
  const [testResults, setTestResults] = useState({});

  // 测试媒体文件列表 (与 Mux Player 测试相同的文件)
  const testMediaFiles = [
    {
      id: 'mp4-video',
      title: 'MP4 视频测试',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'video',
      format: 'MP4'
    },
    {
      id: 'hls-video',
      title: 'HLS 流媒体测试',
      url: 'https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8',
      type: 'video',
      format: 'HLS'
    },
    {
      id: 'webm-video',
      title: 'WebM - Elephants Dream',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/ElephantsDream.webm',
      type: 'video',
      format: 'WebM (VP8)'
    },
    {
      id: 'youtube-video-1',
      title: 'YouTube - Big Buck Bunny',
      url: 'https://www.youtube.com/watch?v=aqz-KE-bpKQ',
      type: 'video',
      format: 'YouTube'
    },
    {
      id: 'youtube-video-2',
      title: 'YouTube - Sintel',
      url: 'https://www.youtube.com/watch?v=eRsGyueVLvQ',
      type: 'video',
      format: 'YouTube'
    },
    {
      id: 'youtube-video-3',
      title: 'YouTube 测试 - Elephants Dream',
      url: 'https://www.youtube.com/watch?v=TLkA0RELQ1g',
      type: 'video',
      format: 'YouTube'
    },
    {
      id: 'dash-video',
      title: 'DASH 视频测试',
      url: 'https://dash.akamaized.net/akamai/bbb_30fps/bbb_30fps.mpd',
      type: 'video',
      format: 'DASH (MPD)'
    },
    {
      id: 'mp3-audio',
      title: 'MP3 音频测试',
      url: 'https://www.soundhelix.com/examples/mp3/SoundHelix-Song-1.mp3',
      type: 'audio',
      format: 'MP3'
    }
  ];

  // 主题选项
  const themeOptions = [
    { value: 'default', label: '默认主题', color: '#007bff' },
    { value: 'dark', label: '暗色主题', color: '#bb86fc' },
    { value: 'blue', label: '蓝色主题', color: '#2196f3' }
  ];

  // 播放指定媒体
  const playMedia = (media) => {
    console.log('🎵 播放媒体:', media.title);
    setCurrentMedia(media);
    
    // 记录测试开始
    setTestResults(prev => ({
      ...prev,
      [media.id]: {
        ...prev[media.id],
        tested: true,
        startTime: new Date().toISOString(),
        status: 'loading'
      }
    }));
  };

  // 事件处理函数
  const handlePlay = () => {
    console.log('▶️ 开始播放');
    if (currentMedia) {
      setTestResults(prev => ({
        ...prev,
        [currentMedia.id]: {
          ...prev[currentMedia.id],
          status: 'success',
          playTime: new Date().toISOString()
        }
      }));
    }
  };

  const handlePause = () => {
    console.log('⏸️ 暂停播放');
  };

  const handleError = (error) => {
    console.error('❌ 播放错误:', error);
    if (currentMedia) {
      setTestResults(prev => ({
        ...prev,
        [currentMedia.id]: {
          ...prev[currentMedia.id],
          status: 'error',
          error: error.message || '播放错误'
        }
      }));
    }
  };

  const handleProgress = (state) => {
    // console.log('📊 播放进度:', state);
  };

  const handleDuration = (duration) => {
    console.log('⏱️ 视频时长:', duration);
  };

  // 获取测试结果状态图标
  const getStatusIcon = (mediaId) => {
    const result = testResults[mediaId];
    if (!result?.tested) return '⚪';
    
    switch (result.status) {
      case 'loading': return '🔄';
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⚪';
    }
  };

  // 显示高级控制面板
  const showAdvancedControls = () => {
    alert('高级控制功能开发中...\n\n将包括：\n- 字幕控制\n- 画质选择\n- 画中画模式\n- 播放列表管理');
  };

  return (
    <div style={{ 
      padding: '20px', 
      fontFamily: 'Arial, sans-serif',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh'
    }}>
      <div style={{ 
        maxWidth: '1400px', 
        margin: '0 auto',
        background: 'rgba(255, 255, 255, 0.95)',
        borderRadius: '12px',
        padding: '30px',
        boxShadow: '0 10px 30px rgba(0,0,0,0.2)'
      }}>
        <h1 style={{ 
          textAlign: 'center', 
          marginBottom: '30px',
          color: '#333',
          fontSize: '28px',
          fontWeight: 'bold'
        }}>
          🎬 ReactPlayer 高级播放器测试
        </h1>

        <div style={{ display: 'flex', gap: '30px' }}>
          {/* 左侧控制面板 (30%) */}
          <div style={{ width: '30%' }}>
            {/* 主题选择 */}
            <div style={{ 
              background: 'white', 
              padding: '20px', 
              borderRadius: '8px',
              marginBottom: '20px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ marginBottom: '15px', color: '#333' }}>🎨 主题选择</h3>
              <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                {themeOptions.map(theme => (
                  <Button
                    key={theme.value}
                    onClick={() => setCurrentTheme(theme.value)}
                    style={{
                      background: currentTheme === theme.value ? theme.color : '#f8f9fa',
                      color: currentTheme === theme.value ? 'white' : '#333',
                      border: `2px solid ${theme.color}`,
                      borderRadius: '6px',
                      padding: '8px 12px',
                      fontSize: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease'
                    }}
                  >
                    {theme.label}
                  </Button>
                ))}
              </div>
            </div>

            {/* 媒体文件列表 */}
            <div style={{ 
              background: 'white', 
              padding: '20px', 
              borderRadius: '8px',
              marginBottom: '20px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h3 style={{ marginBottom: '15px', color: '#333' }}>📁 测试媒体文件</h3>
              <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                {testMediaFiles.map(media => (
                  <Button
                    key={media.id}
                    onClick={() => playMedia(media)}
                    style={{
                      background: currentMedia?.id === media.id ? '#007bff' : '#f8f9fa',
                      color: currentMedia?.id === media.id ? 'white' : '#333',
                      border: '1px solid #dee2e6',
                      borderRadius: '6px',
                      padding: '12px',
                      textAlign: 'left',
                      cursor: 'pointer',
                      transition: 'all 0.3s ease',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px'
                    }}
                  >
                    <span style={{ fontSize: '16px' }}>{getStatusIcon(media.id)}</span>
                    <div>
                      <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                        {media.title}
                      </div>
                      <div style={{ fontSize: '12px', opacity: 0.7 }}>
                        {media.format} • {media.type}
                      </div>
                    </div>
                  </Button>
                ))}
              </div>
            </div>

            {/* 测试结果 */}
            <div style={{ 
              background: 'white', 
              padding: '20px', 
              borderRadius: '8px',
              marginBottom: '20px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h4 style={{ marginBottom: '15px', color: '#333' }}>📊 测试结果：</h4>
              <div style={{ fontSize: '14px' }}>
                {testMediaFiles.map(media => {
                  const result = testResults[media.id];
                  if (!result?.tested) return null;
                  
                  return (
                    <div key={media.id} style={{ 
                      marginBottom: '8px',
                      padding: '8px',
                      borderRadius: '4px',
                      background: result.status === 'success' ? '#d4edda' : 
                                 result.status === 'error' ? '#f8d7da' : '#fff3cd'
                    }}>
                      <div style={{ fontWeight: 'bold' }}>
                        {getStatusIcon(media.id)} {media.format}
                      </div>
                      {result.status === 'error' && (
                        <div style={{ color: '#721c24', fontSize: '12px' }}>
                          错误: {result.error}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>

            {/* 高级控制 */}
            <Button 
              onClick={showAdvancedControls}
              style={{
                width: '100%',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '6px',
                padding: '12px',
                fontSize: '14px',
                cursor: 'pointer',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                gap: '8px'
              }}
            >
              <Icon name="settings" />
              显示高级控制
            </Button>
          </div>

          {/* 右侧播放器区域 (70%) */}
          <div style={{ width: '70%' }}>
            {currentMedia ? (
              <div>
                <h2 style={{ 
                  marginBottom: '20px', 
                  color: '#333',
                  fontSize: '20px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '10px'
                }}>
                  正在播放: {currentMedia.title}
                  <span style={{ 
                    fontSize: '14px', 
                    background: '#007bff', 
                    color: 'white', 
                    padding: '4px 8px', 
                    borderRadius: '4px' 
                  }}>
                    {currentMedia.format}
                  </span>
                </h2>

                {/* ReactPlayer 高级播放器 */}
                <div style={{ marginBottom: '20px' }}>
                  <ReactPlayerAdvanced
                    url={currentMedia.url}
                    width="100%"
                    height="auto"
                    theme={currentTheme}
                    controls={true}
                    onPlay={handlePlay}
                    onPause={handlePause}
                    onProgress={handleProgress}
                    onDuration={handleDuration}
                    onError={handleError}
                    style={{
                      aspectRatio: currentMedia.type === 'video' ? '16/9' : 'auto',
                      minHeight: currentMedia.type === 'audio' ? '200px' : 'auto'
                    }}
                  />
                </div>

                {/* 播放器信息 */}
                <div style={{ 
                  background: 'white', 
                  padding: '20px', 
                  borderRadius: '8px',
                  boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
                }}>
                  <h4 style={{ marginBottom: '15px', color: '#333' }}>ℹ️ 播放器信息：</h4>
                  <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
                    gap: '15px',
                    fontSize: '14px'
                  }}>
                    <div>
                      <strong>播放器类型:</strong> ReactPlayer Advanced
                    </div>
                    <div>
                      <strong>当前主题:</strong> {themeOptions.find(t => t.value === currentTheme)?.label}
                    </div>
                    <div>
                      <strong>媒体格式:</strong> {currentMedia.format}
                    </div>
                    <div>
                      <strong>媒体类型:</strong> {currentMedia.type}
                    </div>
                    <div>
                      <strong>源地址:</strong> 
                      <a href={currentMedia.url} target="_blank" rel="noopener noreferrer" style={{ 
                        color: '#007bff', 
                        textDecoration: 'none',
                        marginLeft: '5px'
                      }}>
                        查看源文件
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div style={{ 
                textAlign: 'center', 
                padding: '100px 20px',
                color: '#666',
                background: 'white',
                borderRadius: '8px',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <Icon name="play-circle" style={{ fontSize: '64px', marginBottom: '20px', color: '#ccc' }} />
                <h3 style={{ marginBottom: '10px' }}>选择一个媒体文件开始播放</h3>
                <p>从左侧列表中选择任意媒体文件来测试 ReactPlayer 的播放能力</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReactPlayerTest;
