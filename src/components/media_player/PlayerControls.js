import React, { useState } from 'react';
import Slider from '../ui/Slider';
import Button from '../ui/Button';
import Icon from '../ui/Icon';

const formatTime = (seconds) => {
    if (isNaN(seconds) || seconds === Infinity) return '00:00';
    const date = new Date(seconds * 1000);
    const hh = date.getUTCHours();
    const mm = date.getUTCMinutes();
    const ss = date.getUTCSeconds().toString().padStart(2, '0');
    if (hh) return `${hh}:${mm.toString().padStart(2, '0')}:${ss}`;
    return `${mm}:${ss}`;
};

const PlayerControls = ({
  playing, onPlayPause, volume, onVolumeChange, muted, onMute,
  playbackRate, onPlaybackRateChange, played, duration,
  onSeek, onSeekMouseDown, onSeekMouseUp, onToggleFullScreen,
  // 字幕相关props
  subtitles, onToggleSubtitles, onSelectSubtitleTrack,
  playedSeconds, loadedSeconds
}) => {
  const [speedPopoverOpen, setSpeedPopoverOpen] = useState(false);
  const [subtitlePopoverOpen, setSubtitlePopoverOpen] = useState(false);

  return (
    <div className="player-controls">
      <Slider
        min={0} max={0.999999} step="any"
        value={played}
        onMouseDown={onSeekMouseDown}
        onChange={onSeek}
        onMouseUp={onSeekMouseUp}
      />
      <div className="player-controls-row">
        <div style={{ display: 'flex', alignItems: 'center', gap: 16 }}>
          <Button type="text" onClick={onPlayPause} icon={<Icon name={playing ? 'pause' : 'play'} style={{ fontSize: 24, color: 'white' }} />} />
          <Button type="text" onClick={onMute} icon={<Icon name={muted ? 'muted' : 'sound'} style={{ color: 'white' }} />} />
          {!muted && <Slider min={0} max={1} step={0.01} value={volume} onChange={onVolumeChange} style={{ width: 80 }} />}
          <span className="player-controls-time">
            {formatTime(playedSeconds || played * duration)} / {formatTime(duration)}
          </span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 16, position: 'relative' }}>
          {/* 字幕控制 */}
          <Button
            type="text"
            onClick={() => setSubtitlePopoverOpen(!subtitlePopoverOpen)}
            icon={<Icon name="captions" style={{ color: subtitles?.enabled ? '#1890ff' : 'white' }} />}
            title="字幕设置"
          />
          {subtitlePopoverOpen && (
            <div className="player-speed-popover" style={{ minWidth: '120px' }}>
              <Button
                type="text"
                style={{ color: 'white', display: 'block', textAlign: 'left' }}
                onClick={() => {
                  onToggleSubtitles();
                  setSubtitlePopoverOpen(false);
                }}
              >
                {subtitles?.enabled ? '关闭字幕' : '开启字幕'}
              </Button>
              {subtitles?.tracks?.length > 0 && subtitles.enabled && (
                <>
                  <div style={{ borderTop: '1px solid rgba(255,255,255,0.2)', margin: '4px 0' }}></div>
                  {subtitles.tracks.map((track, index) => (
                    <Button
                      key={index}
                      type="text"
                      style={{
                        color: subtitles.currentTrack === index ? '#1890ff' : 'white',
                        display: 'block',
                        textAlign: 'left'
                      }}
                      onClick={() => {
                        onSelectSubtitleTrack(index);
                        setSubtitlePopoverOpen(false);
                      }}
                    >
                      {track.label || `字幕 ${index + 1}`}
                    </Button>
                  ))}
                </>
              )}
            </div>
          )}

          {/* 播放速度控制 */}
          <Button type="text" onClick={() => setSpeedPopoverOpen(!speedPopoverOpen)} style={{ color: 'white' }}>{playbackRate}x</Button>
          {speedPopoverOpen && (
            <div className="player-speed-popover">
              {[0.5, 0.75, 1, 1.25, 1.5, 2].map(rate => (
                <Button key={rate} type="text" style={{ color: 'white', display: 'block' }} onClick={() => { onPlaybackRateChange(rate); setSpeedPopoverOpen(false); }}>{rate}x</Button>
              ))}
            </div>
          )}

          {/* 全屏控制 */}
          <Button type="text" onClick={onToggleFullScreen} icon={<Icon name="fullscreen" style={{ color: 'white' }} />} />
        </div>
      </div>
    </div>
  );
};

export default PlayerControls;
