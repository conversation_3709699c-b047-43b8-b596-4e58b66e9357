import React, { useState, useRef } from 'react';
import MuxPlayer from '@mux/mux-player-react';
import Button from '../ui/Button';
import Icon from '../ui/Icon';

const MuxPlayerTestNew = () => {
  const playerRef = useRef(null);
  const [currentMedia, setCurrentMedia] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(1);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [customTheme, setCustomTheme] = useState('default');
  const [testResults, setTestResults] = useState({});
  const [showNativePlayer, setShowNativePlayer] = useState(false);

  // 测试媒体文件列表
  const testMediaFiles = [
    {
      id: 'local-mp4-video',
      title: '本地MP4 文件测试',
      url: '/videos/Agentic_AI_Workflows_vs_ agents-1920x1080.mp4',
      type: 'video',
      format: 'MP4'
    },
    {
      id: 'mp4-video',
      title: 'MP4 视频测试',
      url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
      type: 'video',
      format: 'MP4'
    },
    {
      id: 'hls-video',
      title: 'HLS 视频测试',
      url: 'https://bitdash-a.akamaihd.net/content/MI201109210084_1/m3u8s/f08e80da-bf1d-4e3d-8899-f0f6155f6efa.m3u8',
      type: 'video',
      format: 'HLS'
    },
    {
      id: 'mux-video-a',
      title: 'Mux 测试视频 A',
      url: 'https://stream.mux.com/maVbJv2GSYNRgS02kPXOOGdJMWGU1mkA019ZUjYE7VU7k',
      type: 'video',
      format: 'Mux HLS'
    },
    {
      id: 'mux-video-b',
      title: 'Mux 测试视频 B',
      url: 'https://stream.mux.com/Sc89iWAyNkhJ3P1rQ02nrEdCFTnfT01CZ2KmaEcxXfB008',
      type: 'video',
      format: 'Mux HLS'
    },
    {
      id: 'mp3-audio-1',
      title: 'MP3 音频测试',
      url: 'https://storage.googleapis.com/media-session/elephants-dream/the-wires.mp3',
      type: 'audio',
      format: 'MP3'
    },
    {
      id: 'mp4-video-1',
      title: 'Local file 1',
      url: '/public/videos/Agentic_AI_Workflows_vs_ agents-1920x1080.mp4',
      type: 'video',
      format: 'MP4'
    },
    {
      id: 'webm-video-2',
      title: 'WebM - Elephants Dream',
      url: 'https://dl6.webmfiles.org/elephants-dream.webm',
      type: 'video',
      format: 'WebM (VP8)'
    },
    {
      id: 'youtube-video-1',
      title: 'YouTube - Big Buck Bunny',
      url: 'https://www.youtube.com/watch?v=YE7VzlLtp-4',
      type: 'video',
      format: 'YouTube'
    },
    {
      id: 'youtube-video-2',
      title: 'YouTube - Sintel',
      url: 'https://www.youtube.com/watch?v=eRsGyueVLvQ',
      type: 'video',
      format: 'YouTube'
    },
    {
      id: 'dash-video',
      title: 'DASH 视频测试',
      url: 'https://dash.akamaized.net/akamai/bbb_30fps/bbb_30fps_640x360_800k.mpd',
      type: 'video',
      format: 'DASH (MPD)'
    }
  ];

  // 主题样式配置
  const themes = {
    default: {},
    dark: {
      '--media-primary-color': '#bb86fc',
      '--media-secondary-color': '#03dac6',
      '--media-text-color': '#ffffff',
      '--media-control-background': 'rgba(0, 0, 0, 0.8)',
      '--media-control-hover-background': 'rgba(187, 134, 252, 0.2)'
    },
    blue: {
      '--media-primary-color': '#2196f3',
      '--media-secondary-color': '#03a9f4',
      '--media-text-color': '#ffffff',
      '--media-control-background': 'rgba(33, 150, 243, 0.1)',
      '--media-control-hover-background': 'rgba(33, 150, 243, 0.2)'
    },
    green: {
      '--media-primary-color': '#4caf50',
      '--media-secondary-color': '#8bc34a',
      '--media-text-color': '#ffffff',
      '--media-control-background': 'rgba(76, 175, 80, 0.1)',
      '--media-control-hover-background': 'rgba(76, 175, 80, 0.2)'
    }
  };

  // 播放指定媒体
  const playMedia = (media) => {
    console.log('🎵 播放媒体:', media.title);

    // 运行诊断
    const diagnosis = diagnoseMediaSupport(media);
    const alternatives = testAlternativePlayback(media);

    setCurrentMedia(media);
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);

    // 记录测试开始
    setTestResults(prev => ({
      ...prev,
      [media.id]: {
        ...prev[media.id],
        tested: true,
        startTime: new Date().toISOString(),
        status: 'loading',
        diagnosis: diagnosis,
        alternatives: alternatives
      }
    }));
  };

  // 播放器事件处理
  const handlePlay = () => {
    console.log('▶️ 播放开始');
    setIsPlaying(true);
  };

  const handlePause = () => {
    console.log('⏸️ 播放暂停');
    setIsPlaying(false);
  };

  const handleTimeUpdate = (e) => {
    const player = e.target;
    setCurrentTime(player.currentTime);
  };

  const handleDurationChange = (e) => {
    const player = e.target;
    setDuration(player.duration);
  };

  const handleVolumeChange = (e) => {
    const player = e.target;
    setVolume(player.volume);
  };

  const handleRateChange = (e) => {
    const player = e.target;
    setPlaybackRate(player.playbackRate);
  };

  const handleLoadedMetadata = (e) => {
    const player = e.target;
    console.log('📊 元数据加载完成:', {
      duration: player.duration,
      videoWidth: player.videoWidth,
      videoHeight: player.videoHeight
    });
    
    // 记录成功加载
    if (currentMedia) {
      setTestResults(prev => ({
        ...prev,
        [currentMedia.id]: {
          ...prev[currentMedia.id],
          status: 'loaded',
          metadata: {
            duration: player.duration,
            videoWidth: player.videoWidth,
            videoHeight: player.videoHeight
          }
        }
      }));
    }
  };

  const handleError = (e) => {
    console.error('❌ 播放错误:', e);

    // 详细错误分析
    const errorDetails = {
      message: e.message || '播放错误',
      code: e.code || 'UNKNOWN',
      type: e.type || 'error',
      target: e.target?.tagName || 'unknown',
      networkState: e.target?.networkState,
      readyState: e.target?.readyState,
      src: e.target?.src || currentMedia?.url
    };

    console.log('详细错误信息:', errorDetails);

    // 记录错误
    if (currentMedia) {
      setTestResults(prev => ({
        ...prev,
        [currentMedia.id]: {
          ...prev[currentMedia.id],
          status: 'error',
          error: errorDetails.message,
          errorDetails: errorDetails
        }
      }));
    }
  };

  // 格式化时间显示
  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 诊断媒体支持情况
  const diagnoseMediaSupport = (media) => {
    const video = document.createElement('video');
    const audio = document.createElement('audio');

    const diagnosis = {
      mediaId: media.id,
      title: media.title,
      url: media.url,
      format: media.format,
      browserSupport: {},
      muxPlayerSupport: 'testing',
      recommendations: []
    };

    // 检查浏览器原生支持
    if (media.format.includes('MP4')) {
      diagnosis.browserSupport.mp4 = video.canPlayType('video/mp4');
    }
    if (media.format.includes('WebM')) {
      diagnosis.browserSupport.webm = video.canPlayType('video/webm');
      diagnosis.browserSupport.webmVP8 = video.canPlayType('video/webm; codecs="vp8"');
      diagnosis.browserSupport.webmVP9 = video.canPlayType('video/webm; codecs="vp9"');
    }
    if (media.format.includes('HLS')) {
      diagnosis.browserSupport.hls = video.canPlayType('application/vnd.apple.mpegurl');
    }
    if (media.format.includes('MP3')) {
      diagnosis.browserSupport.mp3 = audio.canPlayType('audio/mpeg');
    }

    // YouTube 特殊处理
    if (media.format.includes('YouTube')) {
      diagnosis.browserSupport.youtube = 'requires_embed';
      diagnosis.recommendations.push('YouTube 需要使用嵌入式播放器或 iframe');
      diagnosis.recommendations.push('直接使用 YouTube URL 通常不被支持');
    }

    // WebM 诊断
    if (media.format.includes('WebM') && diagnosis.browserSupport.webm === '') {
      diagnosis.recommendations.push('浏览器不支持 WebM 格式');
      diagnosis.recommendations.push('建议使用 MP4 格式作为替代');
    }

    // CORS 检查
    if (media.url.includes('youtube.com') || media.url.includes('youtu.be')) {
      diagnosis.recommendations.push('YouTube 有 CORS 限制，不能直接播放');
    }

    console.log('媒体诊断结果:', diagnosis);
    return diagnosis;
  };

  // 测试替代播放方式
  const testAlternativePlayback = (media) => {
    console.log('🔍 测试替代播放方式:', media.title);

    // 对于 YouTube 视频，尝试不同的方法
    if (media.url.includes('youtube.com/watch')) {
      const videoId = media.url.split('v=')[1]?.split('&')[0];
      if (videoId) {
        const alternatives = {
          embed: `https://www.youtube.com/embed/${videoId}`,
          thumbnail: `https://img.youtube.com/vi/${videoId}/maxresdefault.jpg`,
          directMp4: `https://www.youtube.com/watch?v=${videoId}` // 这通常不工作
        };
        console.log('YouTube 替代方案:', alternatives);
        return alternatives;
      }
    }

    // 对于 WebM，检查是否有 MP4 替代
    if (media.url.includes('.webm')) {
      const mp4Alternative = media.url.replace('.webm', '.mp4');
      console.log('WebM 的 MP4 替代:', mp4Alternative);
      return { mp4Alternative };
    }

    return null;
  };

  return (
    <div style={{ 
      display: 'flex', 
      height: '100vh', 
      overflow: 'hidden',
      background: '#f8f9fa'
    }}>
      {/* 左侧控制面板 */}
      <div style={{ 
        width: '30%', 
        padding: '20px',
        background: 'white',
        borderRight: '1px solid #e2e8f0',
        overflowY: 'auto',
        boxShadow: '2px 0 4px rgba(0,0,0,0.1)'
      }}>
        <h2 style={{ marginBottom: '20px', color: '#333', fontSize: '18px' }}>
          🎬 Mux Player 测试
        </h2>
        
        {/* 媒体选择区域 */}
        <div style={{ marginBottom: '25px' }}>
          <h4 style={{ marginBottom: '15px', color: '#555', fontSize: '14px', fontWeight: 'bold' }}>
            📁 选择测试媒体：
          </h4>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
            {testMediaFiles.map((media) => (
              <Button
                key={media.id}
                onClick={() => playMedia(media)}
                type={currentMedia?.id === media.id ? 'primary' : 'default'}
                size="small"
                style={{ 
                  padding: '10px',
                  textAlign: 'left',
                  height: 'auto',
                  whiteSpace: 'normal',
                  fontSize: '12px'
                }}
              >
                <div>
                  <div style={{ fontWeight: 'bold', marginBottom: '2px' }}>
                    {media.title}
                  </div>
                  <div style={{ fontSize: '11px', opacity: 0.7 }}>
                    {media.format} • {media.type}
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* 高级控制面板 */}
        <div style={{ marginBottom: '25px' }}>
          <Button
            onClick={() => setShowAdvanced(!showAdvanced)}
            size="small"
            style={{ marginBottom: '15px', width: '100%' }}
          >
            {showAdvanced ? '隐藏' : '显示'} 高级控制
          </Button>

          {showAdvanced && (
            <div style={{
              background: '#f8f9fa',
              padding: '12px',
              borderRadius: '6px',
              border: '1px solid #dee2e6'
            }}>
              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', fontSize: '12px' }}>
                  主题样式:
                </label>
                <select
                  value={customTheme}
                  onChange={(e) => setCustomTheme(e.target.value)}
                  style={{ width: '100%', padding: '6px', borderRadius: '4px', border: '1px solid #ccc', fontSize: '12px' }}
                >
                  <option value="default">默认主题</option>
                  <option value="dark">深色主题</option>
                  <option value="blue">蓝色主题</option>
                  <option value="green">绿色主题</option>
                </select>
              </div>

              <div style={{ marginBottom: '15px' }}>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', fontSize: '12px' }}>
                  播放速度:
                </label>
                <div style={{ display: 'grid', gridTemplateColumns: 'repeat(3, 1fr)', gap: '4px' }}>
                  {[0.5, 0.75, 1, 1.25, 1.5, 2].map(rate => (
                    <Button
                      key={rate}
                      size="small"
                      type={playbackRate === rate ? 'primary' : 'default'}
                      onClick={() => {
                        if (playerRef.current) {
                          playerRef.current.playbackRate = rate;
                          setPlaybackRate(rate);
                        }
                      }}
                      style={{ fontSize: '11px', padding: '4px' }}
                    >
                      {rate}x
                    </Button>
                  ))}
                </div>
              </div>

              <div>
                <label style={{ display: 'block', marginBottom: '5px', fontWeight: 'bold', fontSize: '12px' }}>
                  音量控制:
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => {
                    const newVolume = parseFloat(e.target.value);
                    if (playerRef.current) {
                      playerRef.current.volume = newVolume;
                      setVolume(newVolume);
                    }
                  }}
                  style={{ width: '100%' }}
                />
                <div style={{ fontSize: '11px', color: '#666', textAlign: 'center' }}>
                  {Math.round(volume * 100)}%
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 测试结果统计 */}
        <div style={{
          background: '#f0f9ff',
          padding: '12px',
          borderRadius: '6px',
          border: '1px solid #0ea5e9'
        }}>
          <h4 style={{ marginBottom: '10px', color: '#0369a1', fontSize: '13px' }}>📊 测试结果：</h4>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr',
            gap: '6px',
            fontSize: '11px'
          }}>
            {testMediaFiles.map((media) => {
              const result = testResults[media.id];
              const getStatusIcon = (status) => {
                switch (status) {
                  case 'loaded': return '✅';
                  case 'error': return '❌';
                  case 'loading': return '⏳';
                  default: return '⚪';
                }
              };

              return (
                <div
                  key={media.id}
                  style={{
                    padding: '4px 8px',
                    background: 'white',
                    borderRadius: '3px',
                    border: '1px solid #e2e8f0'
                  }}
                >
                  <div style={{ fontWeight: 'bold' }}>
                    {getStatusIcon(result?.status)} {media.format}
                  </div>
                  {result?.error && (
                    <div style={{ fontSize: '10px', color: '#dc2626', marginTop: '2px' }}>
                      错误: {result.error}
                    </div>
                  )}
                </div>
              );
            })}
          </div>

          <div style={{ marginTop: '10px', fontSize: '11px' }}>
            <strong>统计：</strong>
            <span style={{ marginLeft: '8px' }}>
              已测试: {Object.keys(testResults).length} / {testMediaFiles.length}
            </span>
            <br />
            <span>
              成功: {Object.values(testResults).filter(r => r.status === 'loaded').length}
            </span>
            <span style={{ marginLeft: '10px' }}>
              失败: {Object.values(testResults).filter(r => r.status === 'error').length}
            </span>
          </div>
        </div>
      </div>

      {/* 右侧播放器区域 */}
      <div style={{
        width: '70%',
        padding: '20px',
        overflowY: 'auto',
        background: '#fafafa'
      }}>
        {currentMedia ? (
          <div>
            <h3 style={{ marginBottom: '15px', color: '#555', fontSize: '18px' }}>
              正在播放: {currentMedia.title}
            </h3>

            {/* 播放器切换按钮 */}
            <div style={{ marginBottom: '15px' }}>
              <Button
                onClick={() => setShowNativePlayer(!showNativePlayer)}
                type={showNativePlayer ? 'primary' : 'default'}
                size="small"
              >
                {showNativePlayer ? '🎬 Mux Player' : '🔧 原生播放器'}
              </Button>
              <span style={{ marginLeft: '10px', fontSize: '14px', color: '#666' }}>
                {showNativePlayer ? '当前：HTML5 原生播放器' : '当前：Mux Player'}
              </span>
            </div>

            <div style={{
              background: '#000',
              borderRadius: '8px',
              overflow: 'hidden',
              position: 'relative',
              aspectRatio: currentMedia.type === 'audio' ? '16/9' : 'auto',
              marginBottom: '20px',
              ...themes[customTheme]
            }}>
              {showNativePlayer ? (
                // 原生 HTML5 播放器
                currentMedia.type === 'video' ? (
                  <video
                    src={currentMedia.url}
                    controls
                    style={{
                      width: '100%',
                      height: 'auto',
                      aspectRatio: '16/9'
                    }}
                    onPlay={handlePlay}
                    onPause={handlePause}
                    onTimeUpdate={handleTimeUpdate}
                    onDurationChange={handleDurationChange}
                    onVolumeChange={handleVolumeChange}
                    onRateChange={handleRateChange}
                    onLoadedMetadata={handleLoadedMetadata}
                    onError={handleError}
                    crossOrigin="anonymous"
                  />
                ) : (
                  <audio
                    src={currentMedia.url}
                    controls
                    style={{
                      width: '100%',
                      height: '60px',
                      marginTop: 'calc(50% - 30px)'
                    }}
                    onPlay={handlePlay}
                    onPause={handlePause}
                    onTimeUpdate={handleTimeUpdate}
                    onDurationChange={handleDurationChange}
                    onVolumeChange={handleVolumeChange}
                    onRateChange={handleRateChange}
                    onLoadedMetadata={handleLoadedMetadata}
                    onError={handleError}
                    crossOrigin="anonymous"
                  />
                )
              ) : (
                // Mux Player
                <MuxPlayer
                  ref={playerRef}
                  src={currentMedia.url}
                  style={{
                    width: '100%',
                    height: currentMedia.type === 'audio' ? '100%' : 'auto',
                    aspectRatio: currentMedia.type === 'video' ? '16/9' : 'auto',
                    ...themes[customTheme]
                  }}
                  controls
                  autoPlay={false}
                  muted={false}
                  loop={false}
                  crossOrigin="anonymous"
                  onPlay={handlePlay}
                  onPause={handlePause}
                  onTimeUpdate={handleTimeUpdate}
                  onDurationChange={handleDurationChange}
                  onVolumeChange={handleVolumeChange}
                  onRateChange={handleRateChange}
                  onLoadedMetadata={handleLoadedMetadata}
                  onError={handleError}
                />
              )}

              {/* 音频文件的可视化背景 */}
              {currentMedia.type === 'audio' && !showNativePlayer && (
                <div style={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  zIndex: -1
                }}>
                  <div style={{ textAlign: 'center', color: 'white' }}>
                    <Icon name="music" style={{ fontSize: '48px', marginBottom: '10px' }} />
                    <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                      {currentMedia.title}
                    </div>
                    <div style={{ fontSize: '14px', opacity: 0.8 }}>
                      音频播放
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* 播放状态信息 */}
            <div style={{
              background: 'white',
              padding: '20px',
              borderRadius: '8px',
              marginBottom: '20px',
              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
            }}>
              <h4 style={{ marginBottom: '15px', color: '#333' }}>播放状态信息：</h4>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))',
                gap: '15px'
              }}>
                <div>
                  <strong>播放状态:</strong> {isPlaying ? '▶️ 播放中' : '⏸️ 已暂停'}
                </div>
                <div>
                  <strong>当前时间:</strong> {formatTime(currentTime)}
                </div>
                <div>
                  <strong>总时长:</strong> {formatTime(duration)}
                </div>
                <div>
                  <strong>音量:</strong> {Math.round(volume * 100)}%
                </div>
                <div>
                  <strong>播放速度:</strong> {playbackRate}x
                </div>
                <div>
                  <strong>媒体格式:</strong> {currentMedia.format}
                </div>
              </div>
            </div>

            {/* 诊断信息面板 */}
            {testResults[currentMedia.id]?.diagnosis && (
              <div style={{
                background: '#fff3cd',
                padding: '20px',
                borderRadius: '8px',
                marginBottom: '20px',
                border: '1px solid #ffeaa7'
              }}>
                <h4 style={{ marginBottom: '15px', color: '#856404' }}>🔍 媒体诊断信息：</h4>

                {/* 浏览器支持情况 */}
                <div style={{ marginBottom: '15px' }}>
                  <strong>浏览器原生支持：</strong>
                  <div style={{ marginTop: '8px', fontSize: '14px' }}>
                    {Object.entries(testResults[currentMedia.id].diagnosis.browserSupport).map(([format, support]) => (
                      <div key={format} style={{ marginBottom: '4px' }}>
                        <span style={{
                          display: 'inline-block',
                          width: '80px',
                          fontWeight: 'bold'
                        }}>
                          {format.toUpperCase()}:
                        </span>
                        <span style={{
                          color: support === 'probably' ? '#28a745' :
                                support === 'maybe' ? '#ffc107' :
                                support === 'requires_embed' ? '#17a2b8' : '#dc3545'
                        }}>
                          {support === 'probably' ? '✅ 完全支持' :
                           support === 'maybe' ? '⚠️ 可能支持' :
                           support === 'requires_embed' ? 'ℹ️ 需要嵌入' :
                           support === '' ? '❌ 不支持' : support}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 建议 */}
                {testResults[currentMedia.id].diagnosis.recommendations.length > 0 && (
                  <div style={{ marginBottom: '15px' }}>
                    <strong>建议：</strong>
                    <ul style={{ marginTop: '8px', marginLeft: '20px', fontSize: '14px' }}>
                      {testResults[currentMedia.id].diagnosis.recommendations.map((rec, index) => (
                        <li key={index} style={{ marginBottom: '4px' }}>{rec}</li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* 替代方案 */}
                {testResults[currentMedia.id].alternatives && (
                  <div>
                    <strong>替代方案：</strong>
                    <div style={{ marginTop: '8px', fontSize: '14px' }}>
                      {Object.entries(testResults[currentMedia.id].alternatives).map(([key, value]) => (
                        <div key={key} style={{ marginBottom: '4px' }}>
                          <strong>{key}:</strong>
                          <a href={value} target="_blank" rel="noopener noreferrer" style={{ marginLeft: '8px', color: '#007bff' }}>
                            {value}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 错误详情 */}
            {testResults[currentMedia.id]?.errorDetails && (
              <div style={{
                background: '#f8d7da',
                padding: '20px',
                borderRadius: '8px',
                marginBottom: '20px',
                border: '1px solid #f5c6cb'
              }}>
                <h4 style={{ marginBottom: '15px', color: '#721c24' }}>❌ 错误详情：</h4>
                <div style={{ fontSize: '14px' }}>
                  {Object.entries(testResults[currentMedia.id].errorDetails).map(([key, value]) => (
                    <div key={key} style={{ marginBottom: '4px' }}>
                      <strong>{key}:</strong> {String(value)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '60vh',
            background: 'white',
            borderRadius: '8px',
            border: '2px dashed #e2e8f0'
          }}>
            <div style={{ textAlign: 'center', color: '#666' }}>
              <Icon name="play" style={{ fontSize: '48px', marginBottom: '15px', opacity: 0.5 }} />
              <h3 style={{ marginBottom: '10px' }}>选择媒体文件开始测试</h3>
              <p>从左侧面板选择一个媒体文件来测试 Mux Player 的功能</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MuxPlayerTestNew;
