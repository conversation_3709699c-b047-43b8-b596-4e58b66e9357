import React from 'react';
import Button from '../ui/Button';
import Icon from '../ui/Icon';

const MediaList = ({ mode, onModeChange, collections, currentMedia, onMediaSelect }) => {
  if (mode === 'minimized') {
    return (
      <Button
        type="primary" shape="circle" icon={<Icon name="menu" />}
        style={{ position: 'absolute', top: '80px', left: '20px', zIndex: 10 }}
        onClick={() => onModeChange('normal')}
      />
    );
  }

  const getIconForType = (type) => {
    switch(type) {
      case 'youtube': return <Icon name="youtube" />;
      case 'audio': return <Icon name="sound" />;
      default: return <Icon name="play" />;
    }
  };

  return (
    <aside className="media-list-sider" style={{ width: mode === 'fullscreen' ? '100%' : '40%' }}>
      <div className="media-list-header">
        <strong>媒体库</strong>
        <div>
          <Button type="text" shape="circle" icon={<Icon name="close" />} onClick={() => onModeChange('minimized')} />
          <Button type="text" shape="circle" icon={<Icon name="fullscreen" />} onClick={() => onModeChange(mode === 'fullscreen' ? 'normal' : 'fullscreen')} />
        </div>
      </div>
      <div className="media-list-collapse">
        {Object.entries(collections).map(([id, { name, items }]) => (
          <details key={id} open>
            <summary>{name} ({items.length})</summary>
            <ul>
              {items.map(item => (
                <li
                  key={item.url}
                  className={`media-list-item ${currentMedia?.url === item.url ? 'active' : ''}`}
                  onClick={() => onMediaSelect(item)}
                >
                  {getIconForType(item.type)}
                  <div>
                    <div className="media-title">{item.title}</div>
                    <div className="media-artist">{item.artist || '未知艺术家'}</div>
                  </div>
                </li>
              ))}
            </ul>
          </details>
        ))}
      </div>
      <div>
        <strong>播放本地文件</strong>
        <input type="file" onChange={(e) => {
          const file = e.target.files[0];
          if(file) {
            onMediaSelect({ title: file.name, url: URL.createObjectURL(file), type: file.type.startsWith('audio') ? 'audio' : 'video' });
            e.target.value = null;
          }
        }} style={{width: '100%', marginTop: '10px'}}/>
      </div>
    </aside>
  );
};

export default MediaList;
