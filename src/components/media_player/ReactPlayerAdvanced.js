import React, { useState, useRef, useEffect } from 'react';
import ReactPlayer from 'react-player';
import Button from '../ui/Button';
import Icon from '../ui/Icon';

const ReactPlayerAdvanced = ({ 
  url, 
  width = '100%', 
  height = 'auto',
  theme = 'default',
  controls = true,
  autoPlay = false,
  muted = false,
  loop = false,
  onPlay,
  onPause,
  onProgress,
  onDuration,
  onError,
  ...props 
}) => {
  const playerRef = useRef(null);
  const containerRef = useRef(null);
  
  // 播放状态
  const [playing, setPlaying] = useState(false);
  const [volume, setVolume] = useState(1);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [duration, setDuration] = useState(0);
  const [currentTime, setCurrentTime] = useState(0);
  const [buffered, setBuffered] = useState(0);
  const [seeking, setSeeking] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const [showSpeedMenu, setShowSpeedMenu] = useState(false);

  // 主题样式配置
  const themes = {
    default: {
      '--player-primary-color': '#007bff',
      '--player-secondary-color': '#6c757d',
      '--player-text-color': '#ffffff',
      '--player-control-background': 'rgba(0, 0, 0, 0.7)',
      '--player-control-hover-background': 'rgba(0, 123, 255, 0.2)',
      '--player-progress-color': '#007bff',
      '--player-progress-buffer-color': 'rgba(255, 255, 255, 0.3)',
      '--player-progress-background': 'rgba(255, 255, 255, 0.2)'
    },
    dark: {
      '--player-primary-color': '#bb86fc',
      '--player-secondary-color': '#03dac6',
      '--player-text-color': '#ffffff',
      '--player-control-background': 'rgba(0, 0, 0, 0.8)',
      '--player-control-hover-background': 'rgba(187, 134, 252, 0.2)',
      '--player-progress-color': '#bb86fc',
      '--player-progress-buffer-color': 'rgba(255, 255, 255, 0.3)',
      '--player-progress-background': 'rgba(255, 255, 255, 0.2)'
    },
    blue: {
      '--player-primary-color': '#2196f3',
      '--player-secondary-color': '#03a9f4',
      '--player-text-color': '#ffffff',
      '--player-control-background': 'rgba(33, 150, 243, 0.1)',
      '--player-control-hover-background': 'rgba(33, 150, 243, 0.2)',
      '--player-progress-color': '#2196f3',
      '--player-progress-buffer-color': 'rgba(255, 255, 255, 0.3)',
      '--player-progress-background': 'rgba(255, 255, 255, 0.2)'
    }
  };

  // 播放速度选项
  const playbackRates = [0.25, 0.5, 0.75, 1, 1.25, 1.5, 1.75, 2];

  // 事件处理函数
  const handlePlay = () => {
    setPlaying(true);
    onPlay && onPlay();
  };

  const handlePause = () => {
    setPlaying(false);
    onPause && onPause();
  };

  const handleProgress = (state) => {
    if (!seeking) {
      setCurrentTime(state.playedSeconds);
      setBuffered(state.loadedSeconds);
    }
    onProgress && onProgress(state);
  };

  const handleDuration = (duration) => {
    setDuration(duration);
    onDuration && onDuration(duration);
  };

  const handleSeekStart = () => {
    setSeeking(true);
  };

  const handleSeekChange = (value) => {
    setCurrentTime(value);
  };

  const handleSeekEnd = (value) => {
    setSeeking(false);
    playerRef.current?.seekTo(value);
  };

  const handleVolumeChange = (newVolume) => {
    setVolume(newVolume);
  };

  const handlePlaybackRateChange = (rate) => {
    setPlaybackRate(rate);
    setShowSpeedMenu(false);
  };

  const togglePlay = () => {
    setPlaying(!playing);
  };

  const toggleMute = () => {
    setVolume(volume === 0 ? 1 : 0);
  };

  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // 格式化时间显示
  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 全屏状态监听
  useEffect(() => {
    const handleFullscreenChange = () => {
      setIsFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // 控制栏自动隐藏
  useEffect(() => {
    let timeout;
    if (playing && showControls) {
      timeout = setTimeout(() => {
        setShowControls(false);
      }, 3000);
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [playing, showControls]);

  const handleMouseMove = () => {
    setShowControls(true);
  };

  return (
    <div 
      ref={containerRef}
      className="react-player-advanced"
      style={{
        position: 'relative',
        width,
        height,
        backgroundColor: '#000',
        borderRadius: '8px',
        overflow: 'hidden',
        ...themes[theme]
      }}
      onMouseMove={handleMouseMove}
      onMouseLeave={() => playing && setShowControls(false)}
    >
      {/* ReactPlayer 核心组件 */}
      <ReactPlayer
        ref={playerRef}
        url={url}
        width="100%"
        height="100%"
        playing={playing}
        volume={volume}
        playbackRate={playbackRate}
        muted={volume === 0}
        loop={loop}
        controls={false} // 使用自定义控制栏
        onPlay={handlePlay}
        onPause={handlePause}
        onProgress={handleProgress}
        onDuration={handleDuration}
        onError={onError}
        config={{
          youtube: {
            playerVars: {
              showinfo: 1,
              controls: 0
            }
          },
          file: {
            attributes: {
              crossOrigin: 'anonymous'
            }
          }
        }}
        {...props}
      />

      {/* 自定义控制栏 */}
      {controls && (
        <div 
          className="player-controls"
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            background: 'var(--player-control-background)',
            padding: '12px 16px',
            transform: showControls ? 'translateY(0)' : 'translateY(100%)',
            transition: 'transform 0.3s ease',
            display: 'flex',
            alignItems: 'center',
            gap: '12px',
            color: 'var(--player-text-color)'
          }}
        >
          {/* 播放/暂停按钮 */}
          <Button
            onClick={togglePlay}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'var(--player-text-color)',
              padding: '8px',
              borderRadius: '4px',
              cursor: 'pointer',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Icon name={playing ? 'pause' : 'play'} style={{ fontSize: '20px' }} />
          </Button>

          {/* 时间显示 */}
          <span style={{ fontSize: '14px', minWidth: '100px' }}>
            {formatTime(currentTime)} / {formatTime(duration)}
          </span>

          {/* 进度条 */}
          <div style={{ flex: 1, position: 'relative', height: '20px', display: 'flex', alignItems: 'center' }}>
            <div 
              style={{
                width: '100%',
                height: '4px',
                background: 'var(--player-progress-background)',
                borderRadius: '2px',
                position: 'relative',
                cursor: 'pointer'
              }}
              onClick={(e) => {
                const rect = e.currentTarget.getBoundingClientRect();
                const percent = (e.clientX - rect.left) / rect.width;
                const seekTime = percent * duration;
                handleSeekEnd(seekTime);
              }}
            >
              {/* 缓冲进度 */}
              <div 
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  height: '100%',
                  background: 'var(--player-progress-buffer-color)',
                  width: `${(buffered / duration) * 100}%`,
                  borderRadius: '2px'
                }}
              />
              
              {/* 播放进度 */}
              <div 
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  height: '100%',
                  background: 'var(--player-progress-color)',
                  width: `${(currentTime / duration) * 100}%`,
                  borderRadius: '2px'
                }}
              />
              
              {/* 进度拖拽点 */}
              <div 
                style={{
                  position: 'absolute',
                  top: '50%',
                  left: `${(currentTime / duration) * 100}%`,
                  transform: 'translate(-50%, -50%)',
                  width: '12px',
                  height: '12px',
                  background: 'var(--player-progress-color)',
                  borderRadius: '50%',
                  cursor: 'pointer'
                }}
              />
            </div>
          </div>

          {/* 音量控制 */}
          <div style={{ position: 'relative' }}>
            <Button
              onClick={toggleMute}
              onMouseEnter={() => setShowVolumeSlider(true)}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'var(--player-text-color)',
                padding: '8px',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              <Icon name={volume === 0 ? 'volume-off' : volume < 0.5 ? 'volume-down' : 'volume-up'} />
            </Button>
            
            {/* 音量滑块 */}
            {showVolumeSlider && (
              <div 
                style={{
                  position: 'absolute',
                  bottom: '100%',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  background: 'var(--player-control-background)',
                  padding: '8px',
                  borderRadius: '4px',
                  marginBottom: '8px'
                }}
                onMouseLeave={() => setShowVolumeSlider(false)}
              >
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={volume}
                  onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                  style={{
                    width: '80px',
                    height: '4px',
                    background: 'var(--player-progress-background)',
                    outline: 'none',
                    borderRadius: '2px'
                  }}
                />
              </div>
            )}
          </div>

          {/* 播放速度 */}
          <div style={{ position: 'relative' }}>
            <Button
              onClick={() => setShowSpeedMenu(!showSpeedMenu)}
              style={{
                background: 'transparent',
                border: 'none',
                color: 'var(--player-text-color)',
                padding: '8px',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '12px'
              }}
            >
              {playbackRate}x
            </Button>
            
            {/* 速度菜单 */}
            {showSpeedMenu && (
              <div 
                style={{
                  position: 'absolute',
                  bottom: '100%',
                  right: 0,
                  background: 'var(--player-control-background)',
                  borderRadius: '4px',
                  marginBottom: '8px',
                  minWidth: '60px'
                }}
              >
                {playbackRates.map(rate => (
                  <div
                    key={rate}
                    onClick={() => handlePlaybackRateChange(rate)}
                    style={{
                      padding: '8px 12px',
                      cursor: 'pointer',
                      fontSize: '12px',
                      background: rate === playbackRate ? 'var(--player-control-hover-background)' : 'transparent',
                      borderRadius: '4px'
                    }}
                  >
                    {rate}x
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* 全屏按钮 */}
          <Button
            onClick={toggleFullscreen}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'var(--player-text-color)',
              padding: '8px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            <Icon name={isFullscreen ? 'fullscreen-exit' : 'fullscreen'} />
          </Button>
        </div>
      )}
    </div>
  );
};

export default ReactPlayerAdvanced;
