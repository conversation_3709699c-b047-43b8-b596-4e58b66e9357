import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SideNav from '../SideNav';

describe('SideNav Component', () => {
  const defaultProps = {
    collapsed: false,
    onMenuClick: jest.fn(),
    activeKey: '1',
    onToggleCollapsed: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders SideNav with toggle button', () => {
    render(<SideNav {...defaultProps} />);
    
    // 检查是否有切换按钮
    const toggleButton = screen.getByRole('button');
    expect(toggleButton).toBeInTheDocument();
    
    // 检查是否显示SmartQ
    expect(screen.getByText('SmartQ')).toBeInTheDocument();
  });

  test('calls onToggleCollapsed when toggle button is clicked', () => {
    render(<SideNav {...defaultProps} />);
    
    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);
    
    expect(defaultProps.onToggleCollapsed).toHaveBeenCalledTimes(1);
  });

  test('hides SmartQ when collapsed', () => {
    render(<SideNav {...defaultProps} collapsed={true} />);

    // 当收起时，SmartQ应该不可见
    expect(screen.queryByText('SmartQ')).not.toBeInTheDocument();
  });

  test('shows SmartQ when expanded', () => {
    render(<SideNav {...defaultProps} collapsed={false} />);

    // 当展开时，SmartQ应该可见
    expect(screen.getByText('SmartQ')).toBeInTheDocument();
  });

  test('applies correct CSS classes based on collapsed state', () => {
    const { rerender } = render(<SideNav {...defaultProps} collapsed={false} />);
    
    let sideNav = screen.getByRole('complementary');
    expect(sideNav).not.toHaveClass('sidenav-collapsed');
    
    rerender(<SideNav {...defaultProps} collapsed={true} />);
    sideNav = screen.getByRole('complementary');
    expect(sideNav).toHaveClass('sidenav-collapsed');
  });
});
