import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import SideNav from '../SideNav';

describe('SideNav Scrolling', () => {
  const defaultProps = {
    collapsed: false,
    onMenuClick: jest.fn(),
    activeKey: '1',
    onToggleCollapsed: jest.fn(),
  };

  test('sidenav has correct CSS classes', () => {
    render(<SideNav {...defaultProps} />);

    const sideNav = screen.getByRole('complementary');

    // 检查CSS类是否正确应用
    expect(sideNav).toHaveClass('sidenav');
    expect(sideNav).not.toHaveClass('sidenav-collapsed');
  });

  test('navigation area exists and is accessible', () => {
    render(<SideNav {...defaultProps} />);

    const navElement = screen.getByRole('navigation');

    // 检查导航区域是否存在
    expect(navElement).toBeInTheDocument();
  });

  test('renders all menu items', () => {
    render(<SideNav {...defaultProps} />);
    
    // 检查所有菜单项是否都能渲染
    expect(screen.getByText('客户洞察')).toBeInTheDocument();
    expect(screen.getByText('产品洞察')).toBeInTheDocument();
    expect(screen.getByText('市场洞察')).toBeInTheDocument();
    expect(screen.getByText('媒体播放器')).toBeInTheDocument();
    expect(screen.getByText('Mux播放器测试')).toBeInTheDocument();
    expect(screen.getByText('MediaChrome测试')).toBeInTheDocument();
  });

  test('user profile section is visible at bottom', () => {
    render(<SideNav {...defaultProps} />);
    
    // 检查用户配置文件部分是否存在
    expect(screen.getByText('John Doe')).toBeInTheDocument();
  });
});
