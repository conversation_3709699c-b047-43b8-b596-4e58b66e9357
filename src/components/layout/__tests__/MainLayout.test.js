import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import SideNav from '../SideNav';

// 简化测试，只测试SideNav的切换功能

describe('SideNav Toggle Integration', () => {
  const mockProps = {
    collapsed: false,
    onMenuClick: jest.fn(),
    activeKey: '1',
    onToggleCollapsed: jest.fn(),
  };

  test('renders SideNav with toggle functionality', () => {
    render(<SideNav {...mockProps} />);

    // 检查是否有侧边导航栏
    const sideNav = screen.getByRole('complementary');
    expect(sideNav).toBeInTheDocument();

    // 检查是否有切换按钮
    const toggleButton = screen.getByRole('button');
    expect(toggleButton).toBeInTheDocument();

    // 检查SmartQ是否显示
    expect(screen.getByText('SmartQ')).toBeInTheDocument();
  });

  test('toggle button calls onToggleCollapsed', () => {
    render(<SideNav {...mockProps} />);

    const toggleButton = screen.getByRole('button');
    fireEvent.click(toggleButton);

    expect(mockProps.onToggleCollapsed).toHaveBeenCalledTimes(1);
  });
});
