import React, { useState, useCallback, useMemo, Suspense } from 'react';
import SideNav from './SideNav';
import Tabs from '../ui/Tabs';
import Button from '../ui/Button';
import Icon from '../ui/Icon';

// Lazy load pages
const PlaceholderPage = React.lazy(() => import('../common/PlaceholderPage'));
const MediaPlayerPage = React.lazy(() => import('../../pages/MediaPlayerPage'));
const MuxPlayerTest = React.lazy(() => import('../media_player/MuxPlayerTestNew'));
const ReactPlayerTest = React.lazy(() => import('../media_player/ReactPlayerTest'));
const SuperPlayerTest = React.lazy(() => import('../../example/CallSuperPlayer'));

const pageComponents = {
  '1': PlaceholderPage,
  '8': MediaPlayerPage,
  '9': MuxPlayerTest,
  '10': ReactPlayerTest,
  '11': SuperPlayerTest,
};

const menuInfo = {
    '1': { label: '关键词洞察' },
    '8': { label: '媒体播放器' },
    '9': { label: 'Mux播放器测试' },
    '10': { label: 'ReactPlayer测试' },
    '11': { label: 'SuperPlayer测试' },
};


const MainLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [activeKey, setActiveKey] = useState('1');
  const [panes, setPanes] = useState([{
    key: '1',
    label: '关键词洞察',
    closable: true,
  }]);

  const addTab = useCallback((key) => {
    if (panes.find(pane => pane.key === key)) {
      setActiveKey(key);
      return;
    }
    const { label } = menuInfo[key] || { label: '新页签' };
    const newPane = { key, label, closable: true };
    setPanes(prevPanes => [...prevPanes, newPane]);
    setActiveKey(key);
  }, [panes]);

  const removeTab = (targetKey) => {
    let newActiveKey = activeKey;
    let lastIndex = -1;
    panes.forEach((pane, i) => { if (pane.key === targetKey) { lastIndex = i - 1; } });
    const newPanes = panes.filter(pane => pane.key !== targetKey);
    if (newPanes.length && newActiveKey === targetKey) {
      newActiveKey = lastIndex >= 0 ? newPanes[lastIndex].key : newPanes[0].key;
    }
    if (newPanes.length === 0) newActiveKey = '';
    setPanes(newPanes);
    setActiveKey(newActiveKey);
  };

  const onMenuClick = (key) => addTab(key);

  const renderTabContent = (paneKey) => {
    const Component = pageComponents[paneKey];
    return Component ? <Component title={menuInfo[paneKey]?.label} /> : <div>页面未找到</div>;
  };

  const tabItems = useMemo(() => panes.map(pane => ({
      key: pane.key,
      label: pane.label,
      closable: pane.closable,
      children: renderTabContent(pane.key),
  })), [panes]);

  return (
    <div className="main-layout">
      <SideNav collapsed={collapsed} onMenuClick={onMenuClick} activeKey={activeKey} />
      
      <div className="main-layout-content-wrapper">
        
        <main className="main-layout-body">
          {panes.length > 0 ? (
            <Suspense fallback={<div className="spinner-overlay"><div className="spinner"></div></div>}>
                <Tabs
                    items={tabItems}
                    activeKey={activeKey}
                    onChange={setActiveKey}
                    onEdit={removeTab}
                />
            </Suspense>
          ) : (
            <div className="main-layout-welcome">
              <h2>欢迎使用！</h2>
              <p>请从左侧导航栏选择一个项目开始。</p>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
