import React from 'react';
import Icon from '../ui/Icon';
import Button from '../ui/Button';

export const sideNavMenuItems = [
    { key: 'grp1', label: '关键词研究', type: 'group', children: [
        { key: '1', icon: 'bulb', label: '客户洞察' },
        { key: '13', icon: 'bulb', label: '产品洞察' },
        { key: '14', icon: 'bulb', label: '市场洞察' },
    ]},
    { key: 'grp3', label: '媒体工具', type: 'group', children: [
        { key: '8', icon: 'youtube', label: '媒体播放器' },
        { key: '9', icon: 'play', label: 'Mux播放器测试' },
        { key: '19', icon: 'star', label: 'MediaChrome测试' },
    ]},
];


const SideNav = ({ collapsed, onMenuClick, activeKey, onToggleCollapsed }) => (
  <aside className={`sidenav ${collapsed ? 'sidenav-collapsed' : ''}`} style={{ width: collapsed ? 80 : 220 }}>
    <div className="sidenav-logo">
      <Button
        type="text"
        icon={<Icon name="menu" />}
        onClick={onToggleCollapsed}
        className="sidenav-toggle-btn"
        style={{
          padding: '8px',
          minWidth: 'auto',
          color: 'var(--primary-color)',
          fontSize: '18px'
        }}
      />
      <Icon name="bulb" />
      {!collapsed && <span>BrandName</span>}
    </div>
    <nav>
      <ul>
        {sideNavMenuItems.map(group => (
          <li key={group.key}>
            <div className="nav-group-title">{group.label}</div>
            <ul>
              {group.children.map(item => (
                <li key={item.key} className="nav-item">
                  <a
                    href={`#${item.key}`}
                    className={item.key === activeKey ? 'active' : ''}
                    onClick={(e) => {
                      e.preventDefault();
                      onMenuClick(item.key);
                    }}
                  >
                    <Icon name={item.icon} />
                    {!collapsed && <span className="nav-item-text">{item.label}</span>}
                  </a>
                </li>
              ))}
            </ul>
          </li>
        ))}
      </ul>
    </nav>
    <div className="sidenav-user-profile">
      <div className="sidenav-user-avatar"><Icon name="user"/></div>
      {!collapsed && <span>John Doe</span>}
      {!collapsed && <Icon name="settings" style={{ cursor: 'pointer' }} />}
    </div>
  </aside>
);

export default SideNav;
