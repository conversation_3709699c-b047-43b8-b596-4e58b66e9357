export const analyzeVideoWithGemini = async (media) => {
  if (!media || !media.url || media.url.startsWith('blob:')) {
    // 在纯 React 版本中，我们用 alert 替代 message
    alert('AI 分析功能仅支持网络视频链接。');
    return null;
  }

  const prompt = `Please provide a concise summary and a list of key takeaways for the video titled "${media.title}" available at this URL: ${media.url}.
  Format the output as a single JSON object with two keys: "summary" (a string) and "takeaways" (an array of strings).`;

  const payload = {
    contents: [{ role: "user", parts: [{ text: prompt }] }],
    generationConfig: {
      responseMimeType: "application/json",
      responseSchema: {
        type: "OBJECT",
        properties: {
          "summary": { "type": "STRING" },
          "takeaways": {
            "type": "ARRAY",
            "items": { "type": "STRING" }
          }
        },
      }
    }
  };

  const apiKey = ""; // 根据说明，此处留空
  const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      throw new Error(`API 请求失败，状态码: ${response.status}`);
    }
    
    const result = await response.json();
    
    if (result.candidates && result.candidates[0]?.content?.parts[0]?.text) {
      return JSON.parse(result.candidates[0].content.parts[0].text);
    } else {
      throw new Error("从 API 返回的响应结构无效。");
    }
  } catch (error) {
    console.error("Gemini API Error:", error);
    alert(`AI 分析失败: ${error.message}`);
    return null;
  }
};
