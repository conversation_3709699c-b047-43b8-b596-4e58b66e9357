import React, { useState } from 'react';
import MediaList from '../components/media_player/MediaList';
import Player from '../components/media_player/Player';

const initialCollections = {
  'fav-1': {
    name: '精选视频',
    items: [
        {
        title: 'MP4介绍视频',
        artist: '<PERSON><PERSON>',
        url: 'https://videocdn.cdnpk.net/videos/8c8ac42c-69e4-4616-9080-eabaf3ba9f95/horizontal/previews/videvo_watermarked/large.mp4',
        type: 'video'
      },
      { title: 'Big Buck Bunny', artist: 'Blender Foundation', url: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4', type: 'video' },
      { title: 'earth day celebration', artist: 'Blender Foundation', url: 'https://videocdn.cdnpk.net/harmony/content/video/partners1801/large_preview/haeae34ef_38481125-001-earth-day-illustration-collection.mp4', type: 'video' },
      { title: 'Youtube LEARN TO DRAW FROM 0 to 100! | Roadmap| DrawlikeaSir', artist: 'Fireship', url: 'https://www.youtube.com/watch?v=1jjmOF1hQqI', type: 'youtube' },
      { title: '测试音频', artist: 'Test', url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav', type: 'audio' },
      // Mux.com 测试视频
      { title: 'Mux Test Video A', artist: 'Mux', url: 'https://stream.mux.com/maVbJv2GSYNRgS02kPXOOGdJMWGU1mkA019ZUjYE7VU7k', type: 'video' },
      { title: 'Mux Test Video B', artist: 'Mux', url: 'https://stream.mux.com/Sc89iWAyNkhJ3P1rQ02nrEdCFTnfT01CZ2KmaEcxXfB008', type: 'video' }
    ],
  },
  'local-videos': {
    name: '本地视频',
    items: [
      {
        title: 'Earth Day Illustration',
        artist: 'Local File',
        url: '/videos/haeae34ef_38481125-001-earth-day-illustration-collection.mp4',
        type: 'video'
      },
      {
        title: 'HD Video Sample',
        artist: 'Local File',
        url: '/videos/hd0067-H264 75.mp4',
        type: 'video'
      },
      {
        title: 'Large Video Sample',
        artist: 'Local File',
        url: '/videos/large.mp4',
        type: 'video'
      }
    ],
  },
  'fav-2': {
    name: '背景音乐',
    items: [
      {
        title: '测试音频 - 铃声',
        artist: 'SoundJay',
        url: 'https://www.soundjay.com/misc/sounds/bell-ringing-05.wav',
        type: 'audio'
      },
      {
        title: '示例音乐 - MP3',
        artist: 'Google',
        url: 'https://storage.googleapis.com/media-session/elephants-dream/the-wires.mp3',
        type: 'audio'
      }
    ]
  },
};

const MediaPlayerPage = () => {
  const [listMode, setListMode] = useState('normal');
  const [collections, setCollections] = useState(initialCollections);
  const [currentMedia, setCurrentMedia] = useState(initialCollections['fav-1'].items[0]);

  const handleAddToCollection = (collectionId, mediaItem) => {
    setCollections(prev => {
      const newCollections = { ...prev };
      const targetCollection = newCollections[collectionId];
      if (!targetCollection.items.find(item => item.url === mediaItem.url)) {
        targetCollection.items.push(mediaItem);
      }
      return newCollections;
    });
  };

  const playerVisible = listMode !== 'fullscreen';
  const listVisible = listMode !== 'minimized';

  return (
    <div style={{ display: 'flex', height: '88%', width: '100%', flexDirection: 'row' }}>
      {listVisible && (
        // <div style={{ display: 'flex', height: '85%', width: '100%', flexDirection: 'row' }}>
        <MediaList
          mode={listMode}
          onModeChange={setListMode}
          collections={collections}
          currentMedia={currentMedia}
          onMediaSelect={setCurrentMedia}
        />
        // </div>
      )}
      {playerVisible && (
        <div style={{ display: 'flex',  width: '100%' }}>
          <Player
            collections={collections}
            onAddToCollection={handleAddToCollection}
            media={currentMedia}
          />
        </div>
      )}
      {listMode === 'minimized' && <MediaList mode="minimized" onModeChange={setListMode} />}
    </div>
  );
};

export default MediaPlayerPage;
