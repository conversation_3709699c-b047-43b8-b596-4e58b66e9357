<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ReactPlayer Test</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/react-player@3.0.0/dist/ReactPlayer.standalone.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #000;
            color: white;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        .player-wrapper {
            position: relative;
            width: 100%;
            height: 400px;
            background: #222;
            margin: 20px 0;
        }
        .controls {
            margin: 20px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        button {
            padding: 10px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .url-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background: #333;
            color: white;
        }
        .log {
            background: #111;
            padding: 10px;
            border-radius: 4px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>ReactPlayer 测试页面</h1>
        
        <div class="controls">
            <input 
                type="text" 
                class="url-input" 
                id="urlInput" 
                placeholder="输入视频URL或选择测试视频"
                value="/videos/large.mp4"
            />
            <button onclick="loadVideo()">加载视频</button>
            <button onclick="togglePlay()">播放/暂停</button>
        </div>
        
        <div class="controls">
            <button onclick="loadTestVideo('mp4')">测试MP4</button>
            <button onclick="loadTestVideo('webm')">测试WebM</button>
            <button onclick="loadTestVideo('youtube')">测试YouTube</button>
            <button onclick="clearLog()">清空日志</button>
        </div>

        <div class="controls">
            <button onclick="loadTestVideo('local1')">本地视频1</button>
            <button onclick="loadTestVideo('local2')">本地视频2</button>
            <button onclick="loadTestVideo('local3')">本地视频3</button>
        </div>
        
        <div class="player-wrapper" id="playerContainer"></div>
        
        <div class="log" id="logContainer"></div>
    </div>

    <script>
        const { useState, useEffect, useRef } = React;
        const { createRoot } = ReactDOM;

        // 日志函数
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `[${timestamp}] ${message}\n`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 测试视频URLs
        const testVideos = {
            mp4: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4',
            webm: 'https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.webm',
            youtube: 'https://www.youtube.com/watch?v=LXb3EKWsInQ',
            local1: '/videos/large.mp4',
            local2: '/videos/hd0067-H264 75.mp4',
            local3: '/videos/haeae34ef_38481125-001-earth-day-illustration-collection.mp4'
        };

        // React组件
        function TestPlayer({ videoUrl, isPlaying }) {
            const playerRef = useRef(null);

            const handleReady = () => {
                log('✅ ReactPlayer: onReady - 播放器已准备就绪');
            };

            const handleStart = () => {
                log('✅ ReactPlayer: onStart - 开始播放');
            };

            const handlePlay = () => {
                log('▶️ ReactPlayer: onPlay - 播放事件');
            };

            const handlePause = () => {
                log('⏸️ ReactPlayer: onPause - 暂停事件');
            };

            const handleProgress = (progress) => {
                // 减少日志频率
                if (progress.played > 0 && Math.floor(progress.played * 10) % 2 === 0) {
                    log(`📊 播放进度: ${Math.floor(progress.played * 100)}%`);
                }
            };

            const handleDuration = (duration) => {
                log(`⏱️ ReactPlayer: onDuration - 获取时长: ${duration} 秒`);
            };

            const handleError = (error) => {
                log(`❌ ReactPlayer: onError - ${error?.message || error}`);
                console.error('ReactPlayer Error:', error);
            };

            const handleBuffer = () => {
                log('🔄 ReactPlayer: onBuffer - 正在缓冲');
            };

            const handleBufferEnd = () => {
                log('✅ ReactPlayer: onBufferEnd - 缓冲结束');
            };

            return React.createElement(ReactPlayer, {
                key: videoUrl, // 当URL改变时重新创建
                ref: playerRef,
                url: videoUrl,
                width: '100%',
                height: '100%',
                playing: isPlaying,
                controls: true,
                pip: false,
                config: {
                    file: {
                        attributes: {
                            crossOrigin: 'anonymous',
                            preload: 'none',
                            playsInline: true,
                            autoPlay: false
                        },
                        forceVideo: true
                    },
                    youtube: {
                        playerVars: {
                            autoplay: 0,
                            controls: 1
                        }
                    }
                },
                onReady: handleReady,
                onStart: handleStart,
                onPlay: handlePlay,
                onPause: handlePause,
                onProgress: handleProgress,
                onDuration: handleDuration,
                onError: handleError,
                onBuffer: handleBuffer,
                onBufferEnd: handleBufferEnd
            });
        }

        // 全局状态
        let currentUrl = testVideos.local1; // 默认使用本地视频
        let isPlaying = false;
        let root;

        function renderPlayer() {
            const container = document.getElementById('playerContainer');
            if (!root) {
                root = createRoot(container);
            }

            const playerComponent = React.createElement(TestPlayer, {
                videoUrl: currentUrl,
                isPlaying: isPlaying
            });

            root.render(playerComponent);
        }

        function loadVideo() {
            const url = document.getElementById('urlInput').value.trim();
            if (!url) {
                log('❌ 请输入视频URL');
                return;
            }

            log(`🔄 加载新视频: ${url}`);
            currentUrl = url;
            isPlaying = false; // 重置播放状态
            renderPlayer();
        }

        function loadTestVideo(type) {
            const url = testVideos[type];
            document.getElementById('urlInput').value = url;
            log(`🔄 加载测试视频 (${type}): ${url}`);
            currentUrl = url;
            isPlaying = false;
            renderPlayer();
        }

        function togglePlay() {
            isPlaying = !isPlaying;
            log(`🎮 切换播放状态: ${isPlaying ? '播放' : '暂停'}`);
            renderPlayer();
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('📄 页面加载完成，初始化ReactPlayer...');
            renderPlayer();
            log('🚀 ReactPlayer 初始化完成');
        });
    </script>
</body>
</html>
