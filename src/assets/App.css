/* --- 全局与重置 --- */
:root {
  --primary-color: #1677ff;
  --primary-color-light: #e6f4ff;
  --border-color: #f0f0f0;
  --text-color: #333;
  --text-color-secondary: #888;
  --bg-color: #f5f5f5;
  --bg-color-light: #ffffff;
  --border-radius: 6px;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow: hidden;
  color: var(--text-color);
  background-color: var(--bg-color);
}

/* --- 布局组件 --- */
.main-layout { display: flex; min-height: 100vh; }
.main-layout-content-wrapper { display: flex; flex-direction: column; flex-grow: 1; }
.main-layout-header {
  padding: 0 24px;
  background: var(--bg-color-light);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  height: 64px;
  flex-shrink: 0;
}
.main-layout-header h4 { margin: 0 0 0 16px; font-size: 18px; }
.main-layout-body { flex-grow: 1; display: flex; overflow: hidden; }
.main-layout-welcome { padding: 40px; text-align: center; width: 100%; }

/* --- SideNav --- */
.sidenav {
  background: var(--bg-color-light);
  transition: width 0.2s;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  position: relative;
}
.sidenav-logo {
  height: 64px; display: flex; align-items: center; justify-content: flex-start;
  font-size: 20px; font-weight: bold; color: var(--primary-color);
  white-space: nowrap; overflow: hidden; flex-shrink: 0;
  padding: 0 8px; gap: 8px;
}
.sidenav-logo .icon { font-size: 28px; }
.sidenav-logo span { margin-left: 8px; }
.sidenav-toggle-btn { flex-shrink: 0; }
.sidenav-collapsed .sidenav-logo { justify-content: center; }
.sidenav-collapsed .sidenav-logo span { display: none; }
.sidenav nav { flex-grow: 1; overflow-y: auto; padding: 8px 0; }
.sidenav ul { list-style: none; padding: 0; margin: 0; }
.sidenav .nav-group-title {
  padding: 8px 16px; margin-top: 16px; color: var(--text-color-secondary);
  font-size: 12px; text-transform: uppercase;
}
.sidenav .nav-item a {
  display: flex; align-items: center; padding: 12px 24px;
  text-decoration: none; color: var(--text-color);
  white-space: nowrap; overflow: hidden;
}
.sidenav .nav-item a:hover { background-color: #f0f0f0; }
.sidenav .nav-item a.active {
  background-color: var(--primary-color-light);
  color: var(--primary-color);
  font-weight: 600;
}
.sidenav .nav-item .icon { margin-right: 16px; font-size: 16px; }
.sidenav-collapsed .nav-item a { justify-content: center; }
.sidenav-collapsed .nav-item .icon { margin-right: 0; }
.sidenav-collapsed .nav-item-text, .sidenav-collapsed .nav-group-title { display: none; }
.sidenav-user-profile {
  padding: 20px 16px; border-top: 1px solid var(--border-color);
  display: flex; align-items: center; justify-content: space-between;
  white-space: nowrap; overflow: hidden;
}
.sidenav-user-info { display: flex; align-items: center; gap: 12px; }
.sidenav-user-avatar { width: 32px; height: 32px; border-radius: 50%; background: #ccc; display: flex; align-items: center; justify-content: center; }
.sidenav-collapsed .sidenav-user-profile { justify-content: center; }
.sidenav-collapsed .sidenav-user-profile span { display: none; }

/* --- 自定义 Tabs --- */
.tabs-container { display: flex; flex-direction: column; width: 100%; height: 100%; background: var(--bg-color-light); }
.tabs-nav {
  display: flex; list-style: none; margin: 0; padding: 0 8px;
  border-bottom: 1px solid var(--border-color); flex-shrink: 0;
}
.tabs-tab {
  padding: 10px 16px; cursor: pointer; border-bottom: 2px solid transparent;
  display: flex; align-items: center; gap: 8px;
}
.tabs-tab:hover { color: var(--primary-color); }
.tabs-tab.active { color: var(--primary-color); border-bottom-color: var(--primary-color); }
.tabs-content { flex-grow: 1; overflow-y: auto; }
.tab-pane { height: 100%; }

/* --- 自定义 UI 组件 --- */
.custom-button {
  padding: 8px 16px; border-radius: var(--border-radius); border: 1px solid transparent;
  cursor: pointer; font-size: 14px; display: inline-flex; align-items: center; gap: 8px;
  transition: background-color 0.2s, border-color 0.2s;
}
.custom-button.primary { background-color: var(--primary-color); color: white; }
.custom-button.primary:hover { background-color: #4096ff; }
.custom-button.text { background: transparent; border: none; }
.custom-button.circle { border-radius: 50%; padding: 8px; }
.custom-button:disabled { cursor: not-allowed; opacity: 0.6; }

.modal-overlay {
  position: fixed; top: 0; left: 0; right: 0; bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex; align-items: center; justify-content: center; z-index: 1000;
}
.modal-content {
  background: white; padding: 24px; border-radius: var(--border-radius);
  min-width: 300px; max-width: 500px;
}
.modal-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 16px; }
.modal-header h3 { margin: 0; }
.modal-footer { margin-top: 24px; text-align: right; }

.spinner-overlay {
  position: absolute; top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(255, 255, 255, 0.7); display: flex; align-items: center; justify-content: center;
}
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: var(--primary-color);
  border-radius: 50%;
  width: 40px; height: 40px;
  animation: spin 1s linear infinite;
}
@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

.custom-slider { -webkit-appearance: none; width: 100%; height: 6px; background: #ddd; border-radius: 3px; outline: none; }
.custom-slider::-webkit-slider-thumb {
  -webkit-appearance: none; appearance: none; width: 16px; height: 16px;
  background: var(--primary-color); border-radius: 50%; cursor: pointer;
}

/* --- Media Player --- */
.media-list-sider {
    background: var(--bg-color-light); padding: 20px; overflow-y: auto; height: 100%;
    border-right: 1px solid var(--border-color);
}
.media-list-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px; }
.media-list-header strong { font-size: 16px; }
.media-list-item {
    display: flex; align-items: center; gap: 12px; padding: 8px 12px;
    border-radius: 4px; cursor: pointer;
}
.media-list-item:hover { background-color: #f0f0f0; }
.media-list-item.active { background-color: var(--primary-color-light); }
.media-list-item.active .media-title { color: var(--primary-color); }
.media-title { font-weight: 500; }
.media-artist { font-size: 12px; color: var(--text-color-secondary); }
.media-list-collapse details { margin-bottom: 12px; }
.media-list-collapse summary { font-weight: 600; cursor: pointer; padding: 8px; }

.player-wrapper { position: relative; width: 100%; height: 100%; background: #000; }
.player-controls {
    position: absolute; bottom: 0; left: 0; right: 0;
    padding: 10px 20px; color: white;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
    opacity: 1; /* 始终显示控制条 */
    transition: opacity 0.3s;
}
/* 可选：在不活动时稍微降低透明度 */
.player-wrapper:not(:hover) .player-controls { opacity: 0.9; }
.player-controls-time { font-family: monospace; font-size: 12px; }
.player-controls-row { display: flex; justify-content: space-between; align-items: center; }
.player-speed-popover {
    position: absolute; bottom: 50px; right: 60px;
    background: rgba(0,0,0,0.8); padding: 8px; border-radius: 4px;
}

/* 改进播放器按钮样式 */
.player-controls .custom-button {
    min-width: 40px;
    min-height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transition: background-color 0.2s;
}
.player-controls .custom-button:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 音频可视化动画 */
@keyframes audioBar0 {
  0% { transform: scaleY(0.3); }
  100% { transform: scaleY(1); }
}

@keyframes audioBar1 {
  0% { transform: scaleY(0.5); }
  100% { transform: scaleY(0.8); }
}

@keyframes audioBar2 {
  0% { transform: scaleY(0.7); }
  100% { transform: scaleY(1.2); }
}
