react refresh:37 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
react-dom.development.js:86  Warning: Received `true` for a non-boolean attribute `webkit-playsinline`.

If you want to write it to the DOM, pass a string instead: webkit-playsinline="true" or webkit-playsinline={value.toString()}.
    at video
    at FilePlayer (http://localhost:3001/static/js/reactPlayerFilePlayer.chunk.js:75:5)
    at Player (http://localhost:3001/static/js/vendors-node_modules_react-player_lib_index_js-node_modules_screenfull_index_js.chunk.js:1435:5)
    at Suspense
    at div
    at _a (http://localhost:3001/static/js/vendors-node_modules_react-player_lib_index_js-node_modules_screenfull_index_js.chunk.js:1766:7)
    at div
    at Player (http://localhost:3001/static/js/src_pages_MediaPlayerPage_js.chunk.js:419:3)
    at div
    at div
    at MediaPlayerPage (http://localhost:3001/static/js/src_pages_MediaPlayerPage_js.chunk.js:1767:82)
    at div
    at div
    at div
    at Tabs (http://localhost:3001/static/js/bundle.js:37382:3)
    at Suspense
    at main
    at div
    at div
    at MainLayout (http://localhost:3001/static/js/bundle.js:36658:84)
    at App
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateProperty$1 @ react-dom.development.js:3765
warnUnknownProperties @ react-dom.development.js:3803
validateProperties$2 @ react-dom.development.js:3827
validatePropertiesInDevelopment @ react-dom.development.js:9541
setInitialProperties @ react-dom.development.js:9830
finalizeInitialChildren @ react-dom.development.js:10950
completeWork @ react-dom.development.js:22232
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopConcurrent @ react-dom.development.js:26582
renderRootConcurrent @ react-dom.development.js:26544
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
[新] 使用 Edge 中的 Copilot 来解释控制台错误: 单击
         
         以说明错误。
        了解更多信息
        不再显示
Player.js:55 === 媒体源改变 ===
Player.js:56 新媒体URL: https://videocdn.cdnpk.net/videos/8c8ac42c-69e4-4616-9080-eabaf3ba9f95/horizontal/previews/videvo_watermarked/large.mp4
Player.js:57 媒体类型: video
Player.js:58 媒体标题: MP4介绍视频
Player.js:62 ReactPlayer.canPlay(): true
Player.js:39 🔄 重置播放器状态
Player.js:100 🚀 Player组件挂载，初始化状态
Player.js:55 === 媒体源改变 ===
Player.js:56 新媒体URL: https://videocdn.cdnpk.net/videos/8c8ac42c-69e4-4616-9080-eabaf3ba9f95/horizontal/previews/videvo_watermarked/large.mp4
Player.js:57 媒体类型: video
Player.js:58 媒体标题: MP4介绍视频
Player.js:62 ReactPlayer.canPlay(): true
Player.js:39 🔄 重置播放器状态
Player.js:100 🚀 Player组件挂载，初始化状态
Player.js:55 === 媒体源改变 ===
Player.js:56 新媒体URL: https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4
Player.js:57 媒体类型: video
Player.js:58 媒体标题: Big Buck Bunny
Player.js:62 ReactPlayer.canPlay(): true
Player.js:39 🔄 重置播放器状态
Player.js:119 播放状态切换: false -> true
Player.js:81 播放器状态变化: playing = true
