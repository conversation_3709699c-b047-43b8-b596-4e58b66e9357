// 最简化的 ReactPlayer 测试版本

import React, { useState, useRef } from 'react';
import ReactPlayer from 'react-player';

const SuperPlayerTest = ({ url }) => {
  console.log('🎬 SuperPlayerTest 初始化，URL:', url);
  
  const [playing, setPlaying] = useState(false);
  const [ready, setReady] = useState(false);
  const [error, setError] = useState(null);
  const playerRef = useRef(null);

  const handleReady = () => {
    console.log('✅ ReactPlayer 准备就绪');
    setReady(true);
    setError(null);
  };

  const handleError = (error) => {
    console.error('❌ 播放错误:', error);
    setError(error);
  };

  const togglePlay = () => {
    setPlaying(!playing);
    console.log('🎮 切换播放状态:', !playing);
  };

  return (
    <div style={{
      position: 'relative',
      width: '100%',
      height: '400px',
      backgroundColor: '#000',
      borderRadius: '8px',
      overflow: 'hidden'
    }}>
      {/* ReactPlayer */}
      <ReactPlayer
        ref={playerRef}
        url={url}
        playing={playing}
        width="100%"
        height="100%"
        controls={true} // 使用原生控制器
        onReady={handleReady}
        onError={handleError}
        config={{
          youtube: {
            playerVars: {
              showinfo: 1,
              controls: 1
            }
          }
        }}
      />

      {/* 状态显示 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '4px',
        fontSize: '12px'
      }}>
        <div>Ready: {ready ? '✅' : '❌'}</div>
        <div>Playing: {playing ? '✅' : '❌'}</div>
        {error && <div style={{color: '#ff6b6b'}}>Error: ❌</div>}
      </div>

      {/* 简单播放按钮 */}
      {ready && (
        <div style={{
          position: 'absolute',
          bottom: '10px',
          left: '10px'
        }}>
          <button
            onClick={togglePlay}
            style={{
              background: '#007bff',
              color: 'white',
              border: 'none',
              padding: '10px 20px',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {playing ? '⏸️ 暂停' : '▶️ 播放'}
          </button>
        </div>
      )}

      {/* 加载指示器 */}
      {!ready && !error && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '18px'
        }}>
          🔄 加载中...
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#ff6b6b',
          fontSize: '16px',
          textAlign: 'center',
          padding: '20px',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: '8px'
        }}>
          ❌ 播放错误<br/>
          {error.toString()}
        </div>
      )}
    </div>
  );
};

export default SuperPlayerTest;
