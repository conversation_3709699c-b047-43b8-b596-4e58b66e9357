import React from "react";
import "../App.css";

// 1. 从 'media-chrome/dist/react' 引入你需要的组件
import {
  MediaController,
  MediaControlBar,
  MediaTimeRange,
  MediaTimeDisplay,
  MediaVolumeRange,
  MediaPlayButton,
  MediaFullscreenButton,
} from "media-chrome/dist/react";

const MediaChrome = () => {
  // 视频文件路径与原生版本相同
  const videoSrc = "/assets/Agentic_AI_Workflows_vs_ agents-1920x1080.mp4";

  return (
    <div className="App">
      <header className="App-header">
        {/* 2. 使用 MediaController 包裹 video 元素 */}
        <MediaController style={{ width: "720px" }}>
          <video
            slot="media"
            src="/videos/Agentic_AI_Workflows_vs_ agents-1920x1080.mp4"
            // "https://stream.mux.com/DS00Spx1CV902MCtPj5WknGlR102V5HFkDe/high.mp4"
            preload="auto"
            muted
            crossOrigin=""
          />

          {/* 3. 自定义控制栏 */}

          <media-control-bar>
            <media-play-button></media-play-button>
            <media-seek-backward-button seekoffset="15"></media-seek-backward-button>
            <media-seek-forward-button seekoffset="15"></media-seek-forward-button>
            <media-mute-button></media-mute-button>
            <media-volume-range></media-volume-range>
            <media-time-range></media-time-range>
            <media-time-display
              showduration=""
              remaining=""
            ></media-time-display>
            <media-captions-button></media-captions-button>
            <media-playback-rate-button></media-playback-rate-button>
            <media-pip-button></media-pip-button>
            <media-airplay-button></media-airplay-button>
            <media-cast-button></media-cast-button>
            <media-fullscreen-button></media-fullscreen-button>
          </media-control-bar>
        </MediaController>
      </header>
    </div>
  );
};

export default MediaChrome;
