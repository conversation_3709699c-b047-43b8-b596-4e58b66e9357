// SuperPlayer 简化版本 - 专注于解决播放问题

import React, { useState, useRef, useEffect } from 'react';
import ReactPlayer from 'react-player';

const SuperPlayerSimple = ({ url }) => {
  // 防止重复初始化日志
  const initLoggedRef = useRef(false);
  if (!initLoggedRef.current) {
    console.log('🎬 SuperPlayer 简化版初始化，URL:', url);
    initLoggedRef.current = true;
  }

  // 基础状态
  const [playing, setPlaying] = useState(false);
  const [volume, setVolume] = useState(0.8);
  const [muted, setMuted] = useState(false);
  const [progress, setProgress] = useState({ playedSeconds: 0, loadedSeconds: 0 });
  const [duration, setDuration] = useState(0);
  const [ready, setReady] = useState(false);
  const [error, setError] = useState(null);
  const [canPlay, setCanPlay] = useState(false);

  const playerRef = useRef(null);

  // 事件处理函数
  const handleReady = () => {
    console.log('✅ ReactPlayer 准备就绪');
    setReady(true);
    setError(null);
  };

  const handleStart = () => {
    console.log('🎬 ReactPlayer 开始播放');
    setCanPlay(true);
  };

  const handlePlay = () => {
    console.log('▶️ ReactPlayer 播放事件');
    setPlaying(true);
  };

  const handlePause = () => {
    console.log('⏸️ ReactPlayer 暂停事件');
    setPlaying(false);
  };

  const handleProgress = (state) => {
    // 减少进度日志频率，只在整数秒时记录
    if (Math.floor(state.playedSeconds) !== Math.floor(progress.playedSeconds)) {
      console.log('📈 播放进度:', Math.floor(state.playedSeconds) + 's');
    }
    setProgress(state);
  };

  const handleDuration = (duration) => {
    console.log('⏱️ 视频时长:', duration.toFixed(2) + 's');
    setDuration(duration);
  };

  const handleError = (error) => {
    console.error('❌ 播放错误:', error);
    setError(error);
    setReady(false);
  };

  const handleBuffer = () => {
    console.log('🔄 缓冲中...');
  };

  const handleBufferEnd = () => {
    console.log('✅ 缓冲完成');
  };

  // 播放控制
  const togglePlay = () => {
    console.log('🎮 切换播放状态:', !playing);
    setPlaying(!playing);
  };

  const toggleMute = () => {
    console.log('🎮 切换静音状态:', !muted);
    setMuted(!muted);
  };

  const handleSeek = (e) => {
    const rect = e.currentTarget.getBoundingClientRect();
    const percent = (e.clientX - rect.left) / rect.width;
    const seekTime = percent * duration;
    console.log('🎮 拖拽到:', seekTime.toFixed(2) + 's');
    if (playerRef.current) {
      playerRef.current.seekTo(seekTime);
    }
  };

  // 时间格式化
  const formatTime = (seconds) => {
    if (!seconds || isNaN(seconds)) return '0:00';
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  // 状态监控 - 减少日志频率，避免无限重渲染
  useEffect(() => {
    console.log('📊 状态更新:', {
      playing,
      ready,
      canPlay,
      duration: duration.toFixed(2),
      error: error ? error.toString() : null
    });
  }, [playing, ready, canPlay, duration, error]); // 移除 progress.playedSeconds 避免频繁更新

  return (
    <div style={{
      position: 'relative',
      width: '100%',
      height: '0',
      paddingTop: '56.25%', // 16:9
      backgroundColor: '#000',
      borderRadius: '8px',
      overflow: 'hidden'
    }}>
      {/* ReactPlayer */}
      <div style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%'
      }}>
        <ReactPlayer
          ref={playerRef}
          url={url}
          playing={playing}
          volume={volume}
          muted={muted}
          width="100%"
          height="100%"
          controls={false}
          onReady={handleReady}
          onStart={handleStart}
          onPlay={handlePlay}
          onPause={handlePause}
          onProgress={handleProgress}
          onDuration={handleDuration}
          onError={handleError}
          onBuffer={handleBuffer}
          onBufferEnd={handleBufferEnd}
          config={{
            youtube: {
              playerVars: {
                showinfo: 1,
                controls: 0,
                modestbranding: 1,
                rel: 0,
                iv_load_policy: 3
              }
            },
            file: {
              attributes: {
                crossOrigin: 'anonymous'
              }
            }
          }}
        />
      </div>

      {/* 播放按钮覆盖层 */}
      {!playing && ready && (
        <div 
          style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            width: '80px',
            height: '80px',
            backgroundColor: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
            fontSize: '32px',
            zIndex: 10
          }}
          onClick={togglePlay}
        >
          ▶️
        </div>
      )}

      {/* 加载指示器 */}
      {!ready && !error && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: 'white',
          fontSize: '18px',
          zIndex: 10
        }}>
          🔄 加载中...
        </div>
      )}

      {/* 错误显示 */}
      {error && (
        <div style={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          color: '#ff6b6b',
          fontSize: '16px',
          textAlign: 'center',
          padding: '20px',
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderRadius: '8px',
          zIndex: 10
        }}>
          ❌ 播放错误<br/>
          {error.toString()}
        </div>
      )}

      {/* 底部控制栏 */}
      {ready && (
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          background: 'linear-gradient(transparent, rgba(0, 0, 0, 0.8))',
          padding: '20px 15px 15px',
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          color: 'white',
          zIndex: 5
        }}>
          {/* 播放/暂停 */}
          <button
            onClick={togglePlay}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'white',
              fontSize: '20px',
              cursor: 'pointer',
              padding: '5px'
            }}
          >
            {playing ? '⏸️' : '▶️'}
          </button>

          {/* 时间 */}
          <span style={{ fontSize: '14px', minWidth: '80px' }}>
            {formatTime(progress.playedSeconds)} / {formatTime(duration)}
          </span>

          {/* 进度条 */}
          <div style={{ flex: 1, height: '20px', display: 'flex', alignItems: 'center' }}>
            <div 
              style={{
                width: '100%',
                height: '4px',
                background: 'rgba(255, 255, 255, 0.3)',
                borderRadius: '2px',
                position: 'relative',
                cursor: 'pointer'
              }}
              onClick={handleSeek}
            >
              <div 
                style={{
                  position: 'absolute',
                  left: 0,
                  top: 0,
                  height: '100%',
                  background: '#ff6b6b',
                  width: `${duration > 0 ? (progress.playedSeconds / duration) * 100 : 0}%`,
                  borderRadius: '2px'
                }}
              />
            </div>
          </div>

          {/* 音量 */}
          <button
            onClick={toggleMute}
            style={{
              background: 'transparent',
              border: 'none',
              color: 'white',
              fontSize: '16px',
              cursor: 'pointer',
              padding: '5px'
            }}
          >
            {muted ? '🔇' : '🔊'}
          </button>
        </div>
      )}

      {/* 调试信息 */}
      <div style={{
        position: 'absolute',
        top: '10px',
        right: '10px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '8px',
        borderRadius: '4px',
        fontSize: '11px',
        maxWidth: '200px',
        zIndex: 10
      }}>
        <div><strong>🔍 状态</strong></div>
        <div>Ready: {ready ? '✅' : '❌'}</div>
        <div>Playing: {playing ? '✅' : '❌'}</div>
        <div>Can Play: {canPlay ? '✅' : '❌'}</div>
        <div>Duration: {duration.toFixed(1)}s</div>
        <div>Time: {progress.playedSeconds.toFixed(1)}s</div>
        {error && <div style={{color: '#ff6b6b'}}>Error: ❌</div>}
      </div>
    </div>
  );
};

export default SuperPlayerSimple;
