// src/App.js

import React, { useState, useEffect } from 'react';
import SuperPlayerTest from './SuperPlayerTest';

function CallSuperPlayer() {
  const [logs, setLogs] = useState([]);
  const [showLogs, setShowLogs] = useState(true);

  // 拦截 console.log 来收集日志
  useEffect(() => {
    const originalLog = console.log;
    const originalError = console.error;

    console.log = (...args) => {
      const timestamp = new Date().toLocaleTimeString();
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');

      setLogs(prev => [...prev.slice(-50), { // 保留最近50条日志
        timestamp,
        type: 'log',
        message
      }]);

      originalLog.apply(console, args);
    };

    console.error = (...args) => {
      const timestamp = new Date().toLocaleTimeString();
      const message = args.map(arg =>
        typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
      ).join(' ');

      setLogs(prev => [...prev.slice(-50), {
        timestamp,
        type: 'error',
        message
      }]);

      originalError.apply(console, args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
    };
  }, []);

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div style={{ maxWidth: '1200px', margin: '20px auto', padding: '20px' }}>
      <div style={{ display: 'flex', gap: '20px' }}>
        {/* 左侧播放器区域 */}
        <div style={{ flex: 1 }}>
          <h1>Super Player - 整合 Mux UI 和 ReactPlayer Core</h1>

          <h2>播放 YouTube 视频</h2>
          <SuperPlayerTest url="https://www.youtube.com/watch?v=LXb3EKWsInQ" />

          <h2 style={{ marginTop: '50px' }}>播放本地 MP4 文件</h2>
          <SuperPlayerTest url="http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4" />

          <h2 style={{ marginTop: '50px' }}>播放 MP3 音频</h2>
          <SuperPlayerTest url="https://storage.googleapis.com/media.mux.com/samples/audio.mp3" />
        </div>

        {/* 右侧调试面板 */}
        {showLogs && (
          <div style={{
            width: '400px',
            background: '#f5f5f5',
            border: '1px solid #ddd',
            borderRadius: '8px',
            padding: '15px'
          }}>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              marginBottom: '10px'
            }}>
              <h3 style={{ margin: 0 }}>🔍 调试日志</h3>
              <div>
                <button
                  onClick={clearLogs}
                  style={{
                    background: '#dc3545',
                    color: 'white',
                    border: 'none',
                    padding: '5px 10px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    marginRight: '5px'
                  }}
                >
                  清空
                </button>
                <button
                  onClick={() => setShowLogs(false)}
                  style={{
                    background: '#6c757d',
                    color: 'white',
                    border: 'none',
                    padding: '5px 10px',
                    borderRadius: '4px',
                    cursor: 'pointer'
                  }}
                >
                  隐藏
                </button>
              </div>
            </div>

            <div style={{
              height: '600px',
              overflow: 'auto',
              background: '#000',
              color: '#00ff00',
              padding: '10px',
              borderRadius: '4px',
              fontSize: '12px',
              fontFamily: 'monospace'
            }}>
              {logs.length === 0 ? (
                <div style={{ color: '#666' }}>等待日志输出...</div>
              ) : (
                logs.map((log, index) => (
                  <div
                    key={index}
                    style={{
                      marginBottom: '5px',
                      color: log.type === 'error' ? '#ff6b6b' : '#00ff00'
                    }}
                  >
                    <span style={{ color: '#888' }}>[{log.timestamp}]</span> {log.message}
                  </div>
                ))
              )}
            </div>
          </div>
        )}
      </div>

      {/* 显示日志按钮 */}
      {!showLogs && (
        <button
          onClick={() => setShowLogs(true)}
          style={{
            position: 'fixed',
            top: '20px',
            right: '20px',
            background: '#007bff',
            color: 'white',
            border: 'none',
            padding: '10px 15px',
            borderRadius: '4px',
            cursor: 'pointer',
            zIndex: 1000
          }}
        >
          显示调试日志
        </button>
      )}
    </div>
  );
}

export default CallSuperPlayer;