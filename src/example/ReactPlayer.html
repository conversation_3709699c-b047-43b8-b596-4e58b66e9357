<html lang="en">

<head>
    <meta charset="utf-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="theme-color" content="#000000">
    <meta name="description" content="Web site created using create-react-app">
    <link rel="apple-touch-icon" href="/logo192.png">
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="/manifest.json">
    <!--
      Notice the use of  in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <title>React App</title>
    <script defer="" src="/static/js/bundle.js"></script>
    <style>
        /* --- 全局与重置 --- */
        :root {
            --primary-color: #1677ff;
            --primary-color-light: #e6f4ff;
            --border-color: #f0f0f0;
            --text-color: #333;
            --text-color-secondary: #888;
            --bg-color: #f5f5f5;
            --bg-color-light: #ffffff;
            --border-radius: 6px;
        }

        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            overflow: hidden;
            color: var(--text-color);
            background-color: var(--bg-color);
        }

        /* --- 布局组件 --- */
        .main-layout {
            display: flex;
            min-height: 100vh;
        }

        .main-layout-content-wrapper {
            display: flex;
            flex-direction: column;
            flex-grow: 1;
        }

        .main-layout-header {
            padding: 0 24px;
            background: var(--bg-color-light);
            border-bottom: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            height: 64px;
            flex-shrink: 0;
        }

        .main-layout-header h4 {
            margin: 0 0 0 16px;
            font-size: 18px;
        }

        .main-layout-body {
            flex-grow: 1;
            display: flex;
            overflow: hidden;
        }

        .main-layout-welcome {
            padding: 40px;
            text-align: center;
            width: 100%;
        }

        /* --- SideNav --- */
        .sidenav {
            background: var(--bg-color-light);
            transition: width 0.2s;
            border-right: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .sidenav-logo {
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            font-weight: bold;
            color: var(--primary-color);
            white-space: nowrap;
            overflow: hidden;
            flex-shrink: 0;
        }

        .sidenav-logo .icon {
            font-size: 28px;
        }

        .sidenav-logo span {
            margin-left: 8px;
        }

        .sidenav nav {
            flex-grow: 1;
            overflow-y: auto;
            padding: 8px 0;
        }

        .sidenav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidenav .nav-group-title {
            padding: 8px 16px;
            margin-top: 16px;
            color: var(--text-color-secondary);
            font-size: 12px;
            text-transform: uppercase;
        }

        .sidenav .nav-item a {
            display: flex;
            align-items: center;
            padding: 12px 24px;
            text-decoration: none;
            color: var(--text-color);
            white-space: nowrap;
            overflow: hidden;
        }

        .sidenav .nav-item a:hover {
            background-color: #f0f0f0;
        }

        .sidenav .nav-item a.active {
            background-color: var(--primary-color-light);
            color: var(--primary-color);
            font-weight: 600;
        }

        .sidenav .nav-item .icon {
            margin-right: 16px;
            font-size: 16px;
        }

        .sidenav-collapsed .nav-item a {
            justify-content: center;
        }

        .sidenav-collapsed .nav-item .icon {
            margin-right: 0;
        }

        .sidenav-collapsed .nav-item-text,
        .sidenav-collapsed .nav-group-title {
            display: none;
        }

        .sidenav-user-profile {
            padding: 20px 16px;
            border-top: 1px solid var(--border-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidenav-user-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .sidenav-user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .sidenav-collapsed .sidenav-user-profile {
            justify-content: center;
        }

        .sidenav-collapsed .sidenav-user-profile span {
            display: none;
        }

        /* --- 自定义 Tabs --- */
        .tabs-container {
            display: flex;
            flex-direction: column;
            height: 100%;
            background: var(--bg-color-light);
        }

        .tabs-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0 8px;
            border-bottom: 1px solid var(--border-color);
            flex-shrink: 0;
        }

        .tabs-tab {
            padding: 10px 16px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tabs-tab:hover {
            color: var(--primary-color);
        }

        .tabs-tab.active {
            color: var(--primary-color);
            border-bottom-color: var(--primary-color);
        }

        .tabs-content {
            flex-grow: 1;
            overflow-y: auto;
        }

        .tab-pane {
            height: 100%;
        }

        /* --- 自定义 UI 组件 --- */
        .custom-button {
            padding: 8px 16px;
            border-radius: var(--border-radius);
            border: 1px solid transparent;
            cursor: pointer;
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s, border-color 0.2s;
        }

        .custom-button.primary {
            background-color: var(--primary-color);
            color: white;
        }

        .custom-button.primary:hover {
            background-color: #4096ff;
        }

        .custom-button.text {
            background: transparent;
            border: none;
        }

        .custom-button.circle {
            border-radius: 50%;
            padding: 8px;
        }

        .custom-button:disabled {
            cursor: not-allowed;
            opacity: 0.6;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 24px;
            border-radius: var(--border-radius);
            min-width: 300px;
            max-width: 500px;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }

        .modal-header h3 {
            margin: 0;
        }

        .modal-footer {
            margin-top: 24px;
            text-align: right;
        }

        .spinner-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.7);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left-color: var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        .custom-slider {
            -webkit-appearance: none;
            width: 100%;
            height: 6px;
            background: #ddd;
            border-radius: 3px;
            outline: none;
        }

        .custom-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: var(--primary-color);
            border-radius: 50%;
            cursor: pointer;
        }

        /* --- Media Player --- */
        .media-list-sider {
            background: var(--bg-color-light);
            padding: 20px;
            overflow-y: auto;
            height: 100%;
            border-right: 1px solid var(--border-color);
        }

        .media-list-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .media-list-header strong {
            font-size: 16px;
        }

        .media-list-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
        }

        .media-list-item:hover {
            background-color: #f0f0f0;
        }

        .media-list-item.active {
            background-color: var(--primary-color-light);
        }

        .media-list-item.active .media-title {
            color: var(--primary-color);
        }

        .media-title {
            font-weight: 500;
        }

        .media-artist {
            font-size: 12px;
            color: var(--text-color-secondary);
        }

        .media-list-collapse details {
            margin-bottom: 12px;
        }

        .media-list-collapse summary {
            font-weight: 600;
            cursor: pointer;
            padding: 8px;
        }

        .player-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
        }

        .player-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 10px 20px;
            color: white;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.8), transparent);
            opacity: 1;
            /* 始终显示控制条 */
            transition: opacity 0.3s;
        }

        /* 可选：在不活动时稍微降低透明度 */
        .player-wrapper:not(:hover) .player-controls {
            opacity: 0.9;
        }

        .player-controls-time {
            font-family: monospace;
            font-size: 12px;
        }

        .player-controls-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .player-speed-popover {
            position: absolute;
            bottom: 50px;
            right: 60px;
            background: rgba(0, 0, 0, 0.8);
            padding: 8px;
            border-radius: 4px;
        }

        /* 改进播放器按钮样式 */
        .player-controls .custom-button {
            min-width: 40px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transition: background-color 0.2s;
        }

        .player-controls .custom-button:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        /* 音频可视化动画 */
        @keyframes audioBar0 {
            0% {
                transform: scaleY(0.3);
            }

            100% {
                transform: scaleY(1);
            }
        }

        @keyframes audioBar1 {
            0% {
                transform: scaleY(0.5);
            }

            100% {
                transform: scaleY(0.8);
            }
        }

        @keyframes audioBar2 {
            0% {
                transform: scaleY(0.7);
            }

            100% {
                transform: scaleY(1.2);
            }
        }

        /*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hc3NldHMvQXBwLmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxrQkFBa0I7QUFDbEI7RUFDRSx3QkFBd0I7RUFDeEIsOEJBQThCO0VBQzlCLHVCQUF1QjtFQUN2QixrQkFBa0I7RUFDbEIsNEJBQTRCO0VBQzVCLG1CQUFtQjtFQUNuQix5QkFBeUI7RUFDekIsb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0UsU0FBUztFQUNUOztjQUVZO0VBQ1osbUNBQW1DO0VBQ25DLGtDQUFrQztFQUNsQyxnQkFBZ0I7RUFDaEIsd0JBQXdCO0VBQ3hCLGlDQUFpQztBQUNuQzs7QUFFQSxpQkFBaUI7QUFDakIsZUFBZSxhQUFhLEVBQUUsaUJBQWlCLEVBQUU7QUFDakQsK0JBQStCLGFBQWEsRUFBRSxzQkFBc0IsRUFBRSxZQUFZLEVBQUU7QUFDcEY7RUFDRSxlQUFlO0VBQ2YsaUNBQWlDO0VBQ2pDLDRDQUE0QztFQUM1QyxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWixjQUFjO0FBQ2hCO0FBQ0EseUJBQXlCLGtCQUFrQixFQUFFLGVBQWUsRUFBRTtBQUM5RCxvQkFBb0IsWUFBWSxFQUFFLGFBQWEsRUFBRSxnQkFBZ0IsRUFBRTtBQUNuRSx1QkFBdUIsYUFBYSxFQUFFLGtCQUFrQixFQUFFLFdBQVcsRUFBRTs7QUFFdkUsb0JBQW9CO0FBQ3BCO0VBQ0UsaUNBQWlDO0VBQ2pDLHNCQUFzQjtFQUN0QiwyQ0FBMkM7RUFDM0MsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixrQkFBa0I7QUFDcEI7QUFDQTtFQUNFLFlBQVksRUFBRSxhQUFhLEVBQUUsbUJBQW1CLEVBQUUsdUJBQXVCO0VBQ3pFLGVBQWUsRUFBRSxpQkFBaUIsRUFBRSwyQkFBMkI7RUFDL0QsbUJBQW1CLEVBQUUsZ0JBQWdCLEVBQUUsY0FBYztBQUN2RDtBQUNBLHNCQUFzQixlQUFlLEVBQUU7QUFDdkMscUJBQXFCLGdCQUFnQixFQUFFO0FBQ3ZDLGVBQWUsWUFBWSxFQUFFLGdCQUFnQixFQUFFLGNBQWMsRUFBRTtBQUMvRCxjQUFjLGdCQUFnQixFQUFFLFVBQVUsRUFBRSxTQUFTLEVBQUU7QUFDdkQ7RUFDRSxpQkFBaUIsRUFBRSxnQkFBZ0IsRUFBRSxrQ0FBa0M7RUFDdkUsZUFBZSxFQUFFLHlCQUF5QjtBQUM1QztBQUNBO0VBQ0UsYUFBYSxFQUFFLG1CQUFtQixFQUFFLGtCQUFrQjtFQUN0RCxxQkFBcUIsRUFBRSx3QkFBd0I7RUFDL0MsbUJBQW1CLEVBQUUsZ0JBQWdCO0FBQ3ZDO0FBQ0EsNkJBQTZCLHlCQUF5QixFQUFFO0FBQ3hEO0VBQ0UsNENBQTRDO0VBQzVDLDJCQUEyQjtFQUMzQixnQkFBZ0I7QUFDbEI7QUFDQSwyQkFBMkIsa0JBQWtCLEVBQUUsZUFBZSxFQUFFO0FBQ2hFLGlDQUFpQyx1QkFBdUIsRUFBRTtBQUMxRCxxQ0FBcUMsZUFBZSxFQUFFO0FBQ3RELHlFQUF5RSxhQUFhLEVBQUU7QUFDeEY7RUFDRSxrQkFBa0IsRUFBRSx5Q0FBeUM7RUFDN0QsYUFBYSxFQUFFLG1CQUFtQixFQUFFLDhCQUE4QjtFQUNsRSxtQkFBbUIsRUFBRSxnQkFBZ0I7QUFDdkM7QUFDQSxxQkFBcUIsYUFBYSxFQUFFLG1CQUFtQixFQUFFLFNBQVMsRUFBRTtBQUNwRSx1QkFBdUIsV0FBVyxFQUFFLFlBQVksRUFBRSxrQkFBa0IsRUFBRSxnQkFBZ0IsRUFBRSxhQUFhLEVBQUUsbUJBQW1CLEVBQUUsdUJBQXVCLEVBQUU7QUFDckosMkNBQTJDLHVCQUF1QixFQUFFO0FBQ3BFLGdEQUFnRCxhQUFhLEVBQUU7O0FBRS9ELHFCQUFxQjtBQUNyQixrQkFBa0IsYUFBYSxFQUFFLHNCQUFzQixFQUFFLFlBQVksRUFBRSxpQ0FBaUMsRUFBRTtBQUMxRztFQUNFLGFBQWEsRUFBRSxnQkFBZ0IsRUFBRSxTQUFTLEVBQUUsY0FBYztFQUMxRCw0Q0FBNEMsRUFBRSxjQUFjO0FBQzlEO0FBQ0E7RUFDRSxrQkFBa0IsRUFBRSxlQUFlLEVBQUUsb0NBQW9DO0VBQ3pFLGFBQWEsRUFBRSxtQkFBbUIsRUFBRSxRQUFRO0FBQzlDO0FBQ0Esa0JBQWtCLDJCQUEyQixFQUFFO0FBQy9DLG1CQUFtQiwyQkFBMkIsRUFBRSx5Q0FBeUMsRUFBRTtBQUMzRixnQkFBZ0IsWUFBWSxFQUFFLGdCQUFnQixFQUFFO0FBQ2hELFlBQVksWUFBWSxFQUFFOztBQUUxQixzQkFBc0I7QUFDdEI7RUFDRSxpQkFBaUIsRUFBRSxtQ0FBbUMsRUFBRSw2QkFBNkI7RUFDckYsZUFBZSxFQUFFLGVBQWUsRUFBRSxvQkFBb0IsRUFBRSxtQkFBbUIsRUFBRSxRQUFRO0VBQ3JGLG9EQUFvRDtBQUN0RDtBQUNBLHlCQUF5QixzQ0FBc0MsRUFBRSxZQUFZLEVBQUU7QUFDL0UsK0JBQStCLHlCQUF5QixFQUFFO0FBQzFELHNCQUFzQix1QkFBdUIsRUFBRSxZQUFZLEVBQUU7QUFDN0Qsd0JBQXdCLGtCQUFrQixFQUFFLFlBQVksRUFBRTtBQUMxRCwwQkFBMEIsbUJBQW1CLEVBQUUsWUFBWSxFQUFFOztBQUU3RDtFQUNFLGVBQWUsRUFBRSxNQUFNLEVBQUUsT0FBTyxFQUFFLFFBQVEsRUFBRSxTQUFTO0VBQ3JELG9DQUFvQztFQUNwQyxhQUFhLEVBQUUsbUJBQW1CLEVBQUUsdUJBQXVCLEVBQUUsYUFBYTtBQUM1RTtBQUNBO0VBQ0UsaUJBQWlCLEVBQUUsYUFBYSxFQUFFLG1DQUFtQztFQUNyRSxnQkFBZ0IsRUFBRSxnQkFBZ0I7QUFDcEM7QUFDQSxnQkFBZ0IsYUFBYSxFQUFFLDhCQUE4QixFQUFFLG1CQUFtQixFQUFFLG1CQUFtQixFQUFFO0FBQ3pHLG1CQUFtQixTQUFTLEVBQUU7QUFDOUIsZ0JBQWdCLGdCQUFnQixFQUFFLGlCQUFpQixFQUFFOztBQUVyRDtFQUNFLGtCQUFrQixFQUFFLE1BQU0sRUFBRSxPQUFPLEVBQUUsUUFBUSxFQUFFLFNBQVM7RUFDeEQsb0NBQW9DLEVBQUUsYUFBYSxFQUFFLG1CQUFtQixFQUFFLHVCQUF1QjtBQUNuRztBQUNBO0VBQ0Usb0NBQW9DO0VBQ3BDLHVDQUF1QztFQUN2QyxrQkFBa0I7RUFDbEIsV0FBVyxFQUFFLFlBQVk7RUFDekIsa0NBQWtDO0FBQ3BDO0FBQ0Esa0JBQWtCLEtBQUssdUJBQXVCLEVBQUUsRUFBRSxPQUFPLHlCQUF5QixFQUFFLEVBQUU7O0FBRXRGLGlCQUFpQix3QkFBd0IsRUFBRSxXQUFXLEVBQUUsV0FBVyxFQUFFLGdCQUFnQixFQUFFLGtCQUFrQixFQUFFLGFBQWEsRUFBRTtBQUMxSCx1Q0FDNEIsZ0JBQWdCLEVBQUUsV0FBVyxFQUFFLFlBQVk7RUFDckUsZ0NBQWdDLEVBQUUsa0JBQWtCLEVBQUUsZUFBZTtBQUN2RTs7QUFFQSx5QkFBeUI7QUFDekI7SUFDSSxpQ0FBaUMsRUFBRSxhQUFhLEVBQUUsZ0JBQWdCLEVBQUUsWUFBWTtJQUNoRiwyQ0FBMkM7QUFDL0M7QUFDQSxxQkFBcUIsYUFBYSxFQUFFLDhCQUE4QixFQUFFLG1CQUFtQixFQUFFLG1CQUFtQixFQUFFO0FBQzlHLDRCQUE0QixlQUFlLEVBQUU7QUFDN0M7SUFDSSxhQUFhLEVBQUUsbUJBQW1CLEVBQUUsU0FBUyxFQUFFLGlCQUFpQjtJQUNoRSxrQkFBa0IsRUFBRSxlQUFlO0FBQ3ZDO0FBQ0EseUJBQXlCLHlCQUF5QixFQUFFO0FBQ3BELDBCQUEwQiw0Q0FBNEMsRUFBRTtBQUN4RSx1Q0FBdUMsMkJBQTJCLEVBQUU7QUFDcEUsZUFBZSxnQkFBZ0IsRUFBRTtBQUNqQyxnQkFBZ0IsZUFBZSxFQUFFLGtDQUFrQyxFQUFFO0FBQ3JFLCtCQUErQixtQkFBbUIsRUFBRTtBQUNwRCwrQkFBK0IsZ0JBQWdCLEVBQUUsZUFBZSxFQUFFLFlBQVksRUFBRTs7QUFFaEYsa0JBQWtCLGtCQUFrQixFQUFFLFdBQVcsRUFBRSxZQUFZLEVBQUUsZ0JBQWdCLEVBQUU7QUFDbkY7SUFDSSxrQkFBa0IsRUFBRSxTQUFTLEVBQUUsT0FBTyxFQUFFLFFBQVE7SUFDaEQsa0JBQWtCLEVBQUUsWUFBWTtJQUNoQyxpRUFBaUU7SUFDakUsVUFBVSxFQUFFLFlBQVk7SUFDeEIsd0JBQXdCO0FBQzVCO0FBQ0Esb0JBQW9CO0FBQ3BCLCtDQUErQyxZQUFZLEVBQUU7QUFDN0Qsd0JBQXdCLHNCQUFzQixFQUFFLGVBQWUsRUFBRTtBQUNqRSx1QkFBdUIsYUFBYSxFQUFFLDhCQUE4QixFQUFFLG1CQUFtQixFQUFFO0FBQzNGO0lBQ0ksa0JBQWtCLEVBQUUsWUFBWSxFQUFFLFdBQVc7SUFDN0MsMkJBQTJCLEVBQUUsWUFBWSxFQUFFLGtCQUFrQjtBQUNqRTs7QUFFQSxjQUFjO0FBQ2Q7SUFDSSxlQUFlO0lBQ2YsZ0JBQWdCO0lBQ2hCLGFBQWE7SUFDYixtQkFBbUI7SUFDbkIsdUJBQXVCO0lBQ3ZCLG9DQUFvQztJQUNwQyxrQkFBa0I7SUFDbEIsaUNBQWlDO0FBQ3JDO0FBQ0E7SUFDSSxvQ0FBb0M7QUFDeEM7O0FBRUEsWUFBWTtBQUNaO0VBQ0UsS0FBSyxzQkFBc0IsRUFBRTtFQUM3QixPQUFPLG9CQUFvQixFQUFFO0FBQy9COztBQUVBO0VBQ0UsS0FBSyxzQkFBc0IsRUFBRTtFQUM3QixPQUFPLHNCQUFzQixFQUFFO0FBQ2pDOztBQUVBO0VBQ0UsS0FBSyxzQkFBc0IsRUFBRTtFQUM3QixPQUFPLHNCQUFzQixFQUFFO0FBQ2pDIiwic291cmNlc0NvbnRlbnQiOlsiLyogLS0tIOWFqOWxgOS4jumHjee9riAtLS0gKi9cbjpyb290IHtcbiAgLS1wcmltYXJ5LWNvbG9yOiAjMTY3N2ZmO1xuICAtLXByaW1hcnktY29sb3ItbGlnaHQ6ICNlNmY0ZmY7XG4gIC0tYm9yZGVyLWNvbG9yOiAjZjBmMGYwO1xuICAtLXRleHQtY29sb3I6ICMzMzM7XG4gIC0tdGV4dC1jb2xvci1zZWNvbmRhcnk6ICM4ODg7XG4gIC0tYmctY29sb3I6ICNmNWY1ZjU7XG4gIC0tYmctY29sb3ItbGlnaHQ6ICNmZmZmZmY7XG4gIC0tYm9yZGVyLXJhZGl1czogNnB4O1xufVxuXG5ib2R5IHtcbiAgbWFyZ2luOiAwO1xuICBmb250LWZhbWlseTogLWFwcGxlLXN5c3RlbSwgQmxpbmtNYWNTeXN0ZW1Gb250LCAnU2Vnb2UgVUknLCAnUm9ib3RvJywgJ094eWdlbicsXG4gICAgJ1VidW50dScsICdDYW50YXJlbGwnLCAnRmlyYSBTYW5zJywgJ0Ryb2lkIFNhbnMnLCAnSGVsdmV0aWNhIE5ldWUnLFxuICAgIHNhbnMtc2VyaWY7XG4gIC13ZWJraXQtZm9udC1zbW9vdGhpbmc6IGFudGlhbGlhc2VkO1xuICAtbW96LW9zeC1mb250LXNtb290aGluZzogZ3JheXNjYWxlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBjb2xvcjogdmFyKC0tdGV4dC1jb2xvcik7XG4gIGJhY2tncm91bmQtY29sb3I6IHZhcigtLWJnLWNvbG9yKTtcbn1cblxuLyogLS0tIOW4g+WxgOe7hOS7tiAtLS0gKi9cbi5tYWluLWxheW91dCB7IGRpc3BsYXk6IGZsZXg7IG1pbi1oZWlnaHQ6IDEwMHZoOyB9XG4ubWFpbi1sYXlvdXQtY29udGVudC13cmFwcGVyIHsgZGlzcGxheTogZmxleDsgZmxleC1kaXJlY3Rpb246IGNvbHVtbjsgZmxleC1ncm93OiAxOyB9XG4ubWFpbi1sYXlvdXQtaGVhZGVyIHtcbiAgcGFkZGluZzogMCAyNHB4O1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1iZy1jb2xvci1saWdodCk7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCB2YXIoLS1ib3JkZXItY29sb3IpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBoZWlnaHQ6IDY0cHg7XG4gIGZsZXgtc2hyaW5rOiAwO1xufVxuLm1haW4tbGF5b3V0LWhlYWRlciBoNCB7IG1hcmdpbjogMCAwIDAgMTZweDsgZm9udC1zaXplOiAxOHB4OyB9XG4ubWFpbi1sYXlvdXQtYm9keSB7IGZsZXgtZ3JvdzogMTsgZGlzcGxheTogZmxleDsgb3ZlcmZsb3c6IGhpZGRlbjsgfVxuLm1haW4tbGF5b3V0LXdlbGNvbWUgeyBwYWRkaW5nOiA0MHB4OyB0ZXh0LWFsaWduOiBjZW50ZXI7IHdpZHRoOiAxMDAlOyB9XG5cbi8qIC0tLSBTaWRlTmF2IC0tLSAqL1xuLnNpZGVuYXYge1xuICBiYWNrZ3JvdW5kOiB2YXIoLS1iZy1jb2xvci1saWdodCk7XG4gIHRyYW5zaXRpb246IHdpZHRoIDAuMnM7XG4gIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHZhcigtLWJvcmRlci1jb2xvcik7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cbi5zaWRlbmF2LWxvZ28ge1xuICBoZWlnaHQ6IDY0cHg7IGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBmb250LXNpemU6IDIwcHg7IGZvbnQtd2VpZ2h0OiBib2xkOyBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7XG4gIHdoaXRlLXNwYWNlOiBub3dyYXA7IG92ZXJmbG93OiBoaWRkZW47IGZsZXgtc2hyaW5rOiAwO1xufVxuLnNpZGVuYXYtbG9nbyAuaWNvbiB7IGZvbnQtc2l6ZTogMjhweDsgfVxuLnNpZGVuYXYtbG9nbyBzcGFuIHsgbWFyZ2luLWxlZnQ6IDhweDsgfVxuLnNpZGVuYXYgbmF2IHsgZmxleC1ncm93OiAxOyBvdmVyZmxvdy15OiBhdXRvOyBwYWRkaW5nOiA4cHggMDsgfVxuLnNpZGVuYXYgdWwgeyBsaXN0LXN0eWxlOiBub25lOyBwYWRkaW5nOiAwOyBtYXJnaW46IDA7IH1cbi5zaWRlbmF2IC5uYXYtZ3JvdXAtdGl0bGUge1xuICBwYWRkaW5nOiA4cHggMTZweDsgbWFyZ2luLXRvcDogMTZweDsgY29sb3I6IHZhcigtLXRleHQtY29sb3Itc2Vjb25kYXJ5KTtcbiAgZm9udC1zaXplOiAxMnB4OyB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xufVxuLnNpZGVuYXYgLm5hdi1pdGVtIGEge1xuICBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBwYWRkaW5nOiAxMnB4IDI0cHg7XG4gIHRleHQtZGVjb3JhdGlvbjogbm9uZTsgY29sb3I6IHZhcigtLXRleHQtY29sb3IpO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwOyBvdmVyZmxvdzogaGlkZGVuO1xufVxuLnNpZGVuYXYgLm5hdi1pdGVtIGE6aG92ZXIgeyBiYWNrZ3JvdW5kLWNvbG9yOiAjZjBmMGYwOyB9XG4uc2lkZW5hdiAubmF2LWl0ZW0gYS5hY3RpdmUge1xuICBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yLWxpZ2h0KTtcbiAgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuLnNpZGVuYXYgLm5hdi1pdGVtIC5pY29uIHsgbWFyZ2luLXJpZ2h0OiAxNnB4OyBmb250LXNpemU6IDE2cHg7IH1cbi5zaWRlbmF2LWNvbGxhcHNlZCAubmF2LWl0ZW0gYSB7IGp1c3RpZnktY29udGVudDogY2VudGVyOyB9XG4uc2lkZW5hdi1jb2xsYXBzZWQgLm5hdi1pdGVtIC5pY29uIHsgbWFyZ2luLXJpZ2h0OiAwOyB9XG4uc2lkZW5hdi1jb2xsYXBzZWQgLm5hdi1pdGVtLXRleHQsIC5zaWRlbmF2LWNvbGxhcHNlZCAubmF2LWdyb3VwLXRpdGxlIHsgZGlzcGxheTogbm9uZTsgfVxuLnNpZGVuYXYtdXNlci1wcm9maWxlIHtcbiAgcGFkZGluZzogMjBweCAxNnB4OyBib3JkZXItdG9wOiAxcHggc29saWQgdmFyKC0tYm9yZGVyLWNvbG9yKTtcbiAgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICB3aGl0ZS1zcGFjZTogbm93cmFwOyBvdmVyZmxvdzogaGlkZGVuO1xufVxuLnNpZGVuYXYtdXNlci1pbmZvIHsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgZ2FwOiAxMnB4OyB9XG4uc2lkZW5hdi11c2VyLWF2YXRhciB7IHdpZHRoOiAzMnB4OyBoZWlnaHQ6IDMycHg7IGJvcmRlci1yYWRpdXM6IDUwJTsgYmFja2dyb3VuZDogI2NjYzsgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsganVzdGlmeS1jb250ZW50OiBjZW50ZXI7IH1cbi5zaWRlbmF2LWNvbGxhcHNlZCAuc2lkZW5hdi11c2VyLXByb2ZpbGUgeyBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgfVxuLnNpZGVuYXYtY29sbGFwc2VkIC5zaWRlbmF2LXVzZXItcHJvZmlsZSBzcGFuIHsgZGlzcGxheTogbm9uZTsgfVxuXG4vKiAtLS0g6Ieq5a6a5LmJIFRhYnMgLS0tICovXG4udGFicy1jb250YWluZXIgeyBkaXNwbGF5OiBmbGV4OyBmbGV4LWRpcmVjdGlvbjogY29sdW1uOyBoZWlnaHQ6IDEwMCU7IGJhY2tncm91bmQ6IHZhcigtLWJnLWNvbG9yLWxpZ2h0KTsgfVxuLnRhYnMtbmF2IHtcbiAgZGlzcGxheTogZmxleDsgbGlzdC1zdHlsZTogbm9uZTsgbWFyZ2luOiAwOyBwYWRkaW5nOiAwIDhweDtcbiAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkIHZhcigtLWJvcmRlci1jb2xvcik7IGZsZXgtc2hyaW5rOiAwO1xufVxuLnRhYnMtdGFiIHtcbiAgcGFkZGluZzogMTBweCAxNnB4OyBjdXJzb3I6IHBvaW50ZXI7IGJvcmRlci1ib3R0b206IDJweCBzb2xpZCB0cmFuc3BhcmVudDtcbiAgZGlzcGxheTogZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgZ2FwOiA4cHg7XG59XG4udGFicy10YWI6aG92ZXIgeyBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7IH1cbi50YWJzLXRhYi5hY3RpdmUgeyBjb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7IGJvcmRlci1ib3R0b20tY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpOyB9XG4udGFicy1jb250ZW50IHsgZmxleC1ncm93OiAxOyBvdmVyZmxvdy15OiBhdXRvOyB9XG4udGFiLXBhbmUgeyBoZWlnaHQ6IDEwMCU7IH1cblxuLyogLS0tIOiHquWumuS5iSBVSSDnu4Tku7YgLS0tICovXG4uY3VzdG9tLWJ1dHRvbiB7XG4gIHBhZGRpbmc6IDhweCAxNnB4OyBib3JkZXItcmFkaXVzOiB2YXIoLS1ib3JkZXItcmFkaXVzKTsgYm9yZGVyOiAxcHggc29saWQgdHJhbnNwYXJlbnQ7XG4gIGN1cnNvcjogcG9pbnRlcjsgZm9udC1zaXplOiAxNHB4OyBkaXNwbGF5OiBpbmxpbmUtZmxleDsgYWxpZ24taXRlbXM6IGNlbnRlcjsgZ2FwOiA4cHg7XG4gIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4ycywgYm9yZGVyLWNvbG9yIDAuMnM7XG59XG4uY3VzdG9tLWJ1dHRvbi5wcmltYXJ5IHsgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7IGNvbG9yOiB3aGl0ZTsgfVxuLmN1c3RvbS1idXR0b24ucHJpbWFyeTpob3ZlciB7IGJhY2tncm91bmQtY29sb3I6ICM0MDk2ZmY7IH1cbi5jdXN0b20tYnV0dG9uLnRleHQgeyBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudDsgYm9yZGVyOiBub25lOyB9XG4uY3VzdG9tLWJ1dHRvbi5jaXJjbGUgeyBib3JkZXItcmFkaXVzOiA1MCU7IHBhZGRpbmc6IDhweDsgfVxuLmN1c3RvbS1idXR0b246ZGlzYWJsZWQgeyBjdXJzb3I6IG5vdC1hbGxvd2VkOyBvcGFjaXR5OiAwLjY7IH1cblxuLm1vZGFsLW92ZXJsYXkge1xuICBwb3NpdGlvbjogZml4ZWQ7IHRvcDogMDsgbGVmdDogMDsgcmlnaHQ6IDA7IGJvdHRvbTogMDtcbiAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgwLCAwLCAwLCAwLjUpO1xuICBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsgei1pbmRleDogMTAwMDtcbn1cbi5tb2RhbC1jb250ZW50IHtcbiAgYmFja2dyb3VuZDogd2hpdGU7IHBhZGRpbmc6IDI0cHg7IGJvcmRlci1yYWRpdXM6IHZhcigtLWJvcmRlci1yYWRpdXMpO1xuICBtaW4td2lkdGg6IDMwMHB4OyBtYXgtd2lkdGg6IDUwMHB4O1xufVxuLm1vZGFsLWhlYWRlciB7IGRpc3BsYXk6IGZsZXg7IGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsgYWxpZ24taXRlbXM6IGNlbnRlcjsgbWFyZ2luLWJvdHRvbTogMTZweDsgfVxuLm1vZGFsLWhlYWRlciBoMyB7IG1hcmdpbjogMDsgfVxuLm1vZGFsLWZvb3RlciB7IG1hcmdpbi10b3A6IDI0cHg7IHRleHQtYWxpZ246IHJpZ2h0OyB9XG5cbi5zcGlubmVyLW92ZXJsYXkge1xuICBwb3NpdGlvbjogYWJzb2x1dGU7IHRvcDogMDsgbGVmdDogMDsgcmlnaHQ6IDA7IGJvdHRvbTogMDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjcpOyBkaXNwbGF5OiBmbGV4OyBhbGlnbi1pdGVtczogY2VudGVyOyBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cbi5zcGlubmVyIHtcbiAgYm9yZGVyOiA0cHggc29saWQgcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBib3JkZXItbGVmdC1jb2xvcjogdmFyKC0tcHJpbWFyeS1jb2xvcik7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgd2lkdGg6IDQwcHg7IGhlaWdodDogNDBweDtcbiAgYW5pbWF0aW9uOiBzcGluIDFzIGxpbmVhciBpbmZpbml0ZTtcbn1cbkBrZXlmcmFtZXMgc3BpbiB7IDAlIHsgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7IH0gMTAwJSB7IHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7IH0gfVxuXG4uY3VzdG9tLXNsaWRlciB7IC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTsgd2lkdGg6IDEwMCU7IGhlaWdodDogNnB4OyBiYWNrZ3JvdW5kOiAjZGRkOyBib3JkZXItcmFkaXVzOiAzcHg7IG91dGxpbmU6IG5vbmU7IH1cbi5jdXN0b20tc2xpZGVyOjotd2Via2l0LXNsaWRlci10aHVtYiB7XG4gIC13ZWJraXQtYXBwZWFyYW5jZTogbm9uZTsgYXBwZWFyYW5jZTogbm9uZTsgd2lkdGg6IDE2cHg7IGhlaWdodDogMTZweDtcbiAgYmFja2dyb3VuZDogdmFyKC0tcHJpbWFyeS1jb2xvcik7IGJvcmRlci1yYWRpdXM6IDUwJTsgY3Vyc29yOiBwb2ludGVyO1xufVxuXG4vKiAtLS0gTWVkaWEgUGxheWVyIC0tLSAqL1xuLm1lZGlhLWxpc3Qtc2lkZXIge1xuICAgIGJhY2tncm91bmQ6IHZhcigtLWJnLWNvbG9yLWxpZ2h0KTsgcGFkZGluZzogMjBweDsgb3ZlcmZsb3cteTogYXV0bzsgaGVpZ2h0OiAxMDAlO1xuICAgIGJvcmRlci1yaWdodDogMXB4IHNvbGlkIHZhcigtLWJvcmRlci1jb2xvcik7XG59XG4ubWVkaWEtbGlzdC1oZWFkZXIgeyBkaXNwbGF5OiBmbGV4OyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47IGFsaWduLWl0ZW1zOiBjZW50ZXI7IG1hcmdpbi1ib3R0b206IDIwcHg7IH1cbi5tZWRpYS1saXN0LWhlYWRlciBzdHJvbmcgeyBmb250LXNpemU6IDE2cHg7IH1cbi5tZWRpYS1saXN0LWl0ZW0ge1xuICAgIGRpc3BsYXk6IGZsZXg7IGFsaWduLWl0ZW1zOiBjZW50ZXI7IGdhcDogMTJweDsgcGFkZGluZzogOHB4IDEycHg7XG4gICAgYm9yZGVyLXJhZGl1czogNHB4OyBjdXJzb3I6IHBvaW50ZXI7XG59XG4ubWVkaWEtbGlzdC1pdGVtOmhvdmVyIHsgYmFja2dyb3VuZC1jb2xvcjogI2YwZjBmMDsgfVxuLm1lZGlhLWxpc3QtaXRlbS5hY3RpdmUgeyBiYWNrZ3JvdW5kLWNvbG9yOiB2YXIoLS1wcmltYXJ5LWNvbG9yLWxpZ2h0KTsgfVxuLm1lZGlhLWxpc3QtaXRlbS5hY3RpdmUgLm1lZGlhLXRpdGxlIHsgY29sb3I6IHZhcigtLXByaW1hcnktY29sb3IpOyB9XG4ubWVkaWEtdGl0bGUgeyBmb250LXdlaWdodDogNTAwOyB9XG4ubWVkaWEtYXJ0aXN0IHsgZm9udC1zaXplOiAxMnB4OyBjb2xvcjogdmFyKC0tdGV4dC1jb2xvci1zZWNvbmRhcnkpOyB9XG4ubWVkaWEtbGlzdC1jb2xsYXBzZSBkZXRhaWxzIHsgbWFyZ2luLWJvdHRvbTogMTJweDsgfVxuLm1lZGlhLWxpc3QtY29sbGFwc2Ugc3VtbWFyeSB7IGZvbnQtd2VpZ2h0OiA2MDA7IGN1cnNvcjogcG9pbnRlcjsgcGFkZGluZzogOHB4OyB9XG5cbi5wbGF5ZXItd3JhcHBlciB7IHBvc2l0aW9uOiByZWxhdGl2ZTsgd2lkdGg6IDEwMCU7IGhlaWdodDogMTAwJTsgYmFja2dyb3VuZDogIzAwMDsgfVxuLnBsYXllci1jb250cm9scyB7XG4gICAgcG9zaXRpb246IGFic29sdXRlOyBib3R0b206IDA7IGxlZnQ6IDA7IHJpZ2h0OiAwO1xuICAgIHBhZGRpbmc6IDEwcHggMjBweDsgY29sb3I6IHdoaXRlO1xuICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCh0byB0b3AsIHJnYmEoMCwwLDAsMC44KSwgdHJhbnNwYXJlbnQpO1xuICAgIG9wYWNpdHk6IDE7IC8qIOWni+e7iOaYvuekuuaOp+WItuadoSAqL1xuICAgIHRyYW5zaXRpb246IG9wYWNpdHkgMC4zcztcbn1cbi8qIOWPr+mAie+8muWcqOS4jea0u+WKqOaXtueojeW+rumZjeS9jumAj+aYjuW6piAqL1xuLnBsYXllci13cmFwcGVyOm5vdCg6aG92ZXIpIC5wbGF5ZXItY29udHJvbHMgeyBvcGFjaXR5OiAwLjk7IH1cbi5wbGF5ZXItY29udHJvbHMtdGltZSB7IGZvbnQtZmFtaWx5OiBtb25vc3BhY2U7IGZvbnQtc2l6ZTogMTJweDsgfVxuLnBsYXllci1jb250cm9scy1yb3cgeyBkaXNwbGF5OiBmbGV4OyBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47IGFsaWduLWl0ZW1zOiBjZW50ZXI7IH1cbi5wbGF5ZXItc3BlZWQtcG9wb3ZlciB7XG4gICAgcG9zaXRpb246IGFic29sdXRlOyBib3R0b206IDUwcHg7IHJpZ2h0OiA2MHB4O1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMCwwLDAsMC44KTsgcGFkZGluZzogOHB4OyBib3JkZXItcmFkaXVzOiA0cHg7XG59XG5cbi8qIOaUuei/m+aSreaUvuWZqOaMiemSruagt+W8jyAqL1xuLnBsYXllci1jb250cm9scyAuY3VzdG9tLWJ1dHRvbiB7XG4gICAgbWluLXdpZHRoOiA0MHB4O1xuICAgIG1pbi1oZWlnaHQ6IDQwcHg7XG4gICAgZGlzcGxheTogZmxleDtcbiAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xKTtcbiAgICBib3JkZXItcmFkaXVzOiA1MCU7XG4gICAgdHJhbnNpdGlvbjogYmFja2dyb3VuZC1jb2xvciAwLjJzO1xufVxuLnBsYXllci1jb250cm9scyAuY3VzdG9tLWJ1dHRvbjpob3ZlciB7XG4gICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xufVxuXG4vKiDpn7PpopHlj6/op4bljJbliqjnlLsgKi9cbkBrZXlmcmFtZXMgYXVkaW9CYXIwIHtcbiAgMCUgeyB0cmFuc2Zvcm06IHNjYWxlWSgwLjMpOyB9XG4gIDEwMCUgeyB0cmFuc2Zvcm06IHNjYWxlWSgxKTsgfVxufVxuXG5Aa2V5ZnJhbWVzIGF1ZGlvQmFyMSB7XG4gIDAlIHsgdHJhbnNmb3JtOiBzY2FsZVkoMC41KTsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZVkoMC44KTsgfVxufVxuXG5Aa2V5ZnJhbWVzIGF1ZGlvQmFyMiB7XG4gIDAlIHsgdHJhbnNmb3JtOiBzY2FsZVkoMC43KTsgfVxuICAxMDAlIHsgdHJhbnNmb3JtOiBzY2FsZVkoMS4yKTsgfVxufVxuIl0sInNvdXJjZVJvb3QiOiIifQ== */
    </style>
    <script src="https://www.gstatic.com/cv/js/sender/v1/cast_sender.js?loadCastFramework=1"></script>
    <script src="//www.gstatic.com/cast/sdk/libs/sender/1.0/cast_framework.js"></script>
    <script src="//www.gstatic.com/eureka/clank/134/cast_sender.js"></script>
</head>

<body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
        <div class="main-layout">
            <aside class="sidenav sidenav-collapsed" style="width: 80px;">
                <div class="sidenav-logo"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1em"
                        height="1em" fill="currentColor" class="icon icon-bulb ">
                        <path
                            d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7z">
                        </path>
                    </svg></div>
                <nav>
                    <ul>
                        <li>
                            <div class="nav-group-title">关键词研究</div>
                            <ul>
                                <li class="nav-item"><a href="#1" class=""><svg xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24" width="1em" height="1em" fill="currentColor"
                                            class="icon icon-bulb ">
                                            <path
                                                d="M9 21c0 .55.45 1 1 1h4c.55 0 1-.45 1-1v-1H9v1zm3-19C8.14 2 5 5.14 5 9c0 2.38 1.19 4.47 3 5.74V17c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-2.26c1.81-1.27 3-3.36 3-5.74 0-3.86-3.14-7-7-7z">
                                            </path>
                                        </svg></a></li>
                            </ul>
                        </li>
                        <li>
                            <div class="nav-group-title">媒体工具</div>
                            <ul>
                                <li class="nav-item"><a href="#8" class="active"><svg xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24" width="1em" height="1em" fill="currentColor"
                                            class="icon icon-youtube ">
                                            <path
                                                d="M10 15l5.19-3L10 9v6zm11.56-7.4c-.77-2.85-3.04-5.12-5.9-5.9C12.1.65 7.9.65 4.44 1.7C1.59 2.47-.68 4.74.09 7.6c.77 2.85 3.04 5.12 5.9 5.9 3.46 1.05 7.66 1.05 11.12 0 2.85-.78 5.13-3.05 5.9-5.9s-.67-5.12-3.56-5.9z">
                                            </path>
                                        </svg></a></li>
                            </ul>
                        </li>
                    </ul>
                </nav>
                <div class="sidenav-user-profile">
                    <div class="sidenav-user-avatar"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                            width="1em" height="1em" fill="currentColor" class="icon icon-user ">
                            <path
                                d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z">
                            </path>
                        </svg></div>
                </div>
            </aside>
            <div class="main-layout-content-wrapper">
                <header class="main-layout-header"><button class="custom-button text  "><svg
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1em" height="1em"
                            fill="currentColor" class="icon icon-menu ">
                            <path d="M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z"></path>
                        </svg></button>
                    <h4>媒体播放器</h4>
                </header>
                <main class="main-layout-body">
                    <div class="tabs-container" style="">
                        <ul class="tabs-nav">
                            <li class="tabs-tab "><span>关键词洞察</span><svg xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24" width="1em" height="1em" fill="currentColor"
                                    class="icon icon-close " style="font-size: 14px; margin-left: 8px;">
                                    <path
                                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                                    </path>
                                </svg></li>
                            <li class="tabs-tab active"><span>媒体播放器</span><svg xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24" width="1em" height="1em" fill="currentColor"
                                    class="icon icon-close " style="font-size: 14px; margin-left: 8px;">
                                    <path
                                        d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                                    </path>
                                </svg></li>
                        </ul>
                        <div class="tabs-content">
                            <div class="tab-pane" style="display: none;">
                                <div class="tab-pane">
                                    <h2>关键词洞察</h2>
                                    <p>这里是 关键词洞察 的详细内容区。</p>
                                </div>
                            </div>
                            <div class="tab-pane" style="display: block;">
                                <div style="display: flex; height: 100%; flex-direction: row;">
                                    <aside class="media-list-sider" style="width: 40%;">
                                        <div class="media-list-header"><strong>媒体库</strong>
                                            <div><button class="custom-button text circle "><svg
                                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                        width="1em" height="1em" fill="currentColor"
                                                        class="icon icon-close ">
                                                        <path
                                                            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z">
                                                        </path>
                                                    </svg></button><button class="custom-button text circle "><svg
                                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                        width="1em" height="1em" fill="currentColor"
                                                        class="icon icon-fullscreen ">
                                                        <path
                                                            d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z">
                                                        </path>
                                                    </svg></button></div>
                                        </div>
                                        <div class="media-list-collapse">
                                            <details open="">
                                                <summary>精选视频 (7)</summary>
                                                <ul>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">MP4介绍视频</div>
                                                            <div class="media-artist">Baidu</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item active"><svg
                                                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                            width="1em" height="1em" fill="currentColor"
                                                            class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">Big Buck Bunny</div>
                                                            <div class="media-artist">Blender Foundation</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">earth day celebration</div>
                                                            <div class="media-artist">Blender Foundation</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-youtube ">
                                                            <path
                                                                d="M10 15l5.19-3L10 9v6zm11.56-7.4c-.77-2.85-3.04-5.12-5.9-5.9C12.1.65 7.9.65 4.44 1.7C1.59 2.47-.68 4.74.09 7.6c.77 2.85 3.04 5.12 5.9 5.9 3.46 1.05 7.66 1.05 11.12 0 2.85-.78 5.13-3.05 5.9-5.9s-.67-5.12-3.56-5.9z">
                                                            </path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">React in 100 Seconds</div>
                                                            <div class="media-artist">Fireship</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-sound ">
                                                            <path
                                                                d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z">
                                                            </path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">测试音频</div>
                                                            <div class="media-artist">Test</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">Mux Test Video A</div>
                                                            <div class="media-artist">Mux</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">Mux Test Video B</div>
                                                            <div class="media-artist">Mux</div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </details>
                                            <details open="">
                                                <summary>本地视频 (3)</summary>
                                                <ul>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">Earth Day Illustration</div>
                                                            <div class="media-artist">Local File</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">HD Video Sample</div>
                                                            <div class="media-artist">Local File</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-play ">
                                                            <path d="M8 5v14l11-7z"></path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">Large Video Sample</div>
                                                            <div class="media-artist">Local File</div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </details>
                                            <details open="">
                                                <summary>背景音乐 (2)</summary>
                                                <ul>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-sound ">
                                                            <path
                                                                d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z">
                                                            </path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">测试音频 - 铃声</div>
                                                            <div class="media-artist">SoundJay</div>
                                                        </div>
                                                    </li>
                                                    <li class="media-list-item "><svg xmlns="http://www.w3.org/2000/svg"
                                                            viewBox="0 0 24 24" width="1em" height="1em"
                                                            fill="currentColor" class="icon icon-sound ">
                                                            <path
                                                                d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z">
                                                            </path>
                                                        </svg>
                                                        <div>
                                                            <div class="media-title">示例音乐 - MP3</div>
                                                            <div class="media-artist">Google</div>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </details>
                                        </div>
                                        <div><strong>播放本地文件</strong><input type="file"
                                                style="width: 100%; margin-top: 10px;"></div>
                                    </aside>
                                    <div style="flex-grow: 1; height: 100%;">
                                        <div class="player-wrapper"><video class="react-player"
                                                src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                                                style="display: block; width: 100%; height: 100%;"></video>
                                            <div class="player-controls"><input type="range" min="0" max="0.999999"
                                                    step="any" class="custom-slider " value="0">
                                                <div class="player-controls-row">
                                                    <div style="display: flex; align-items: center; gap: 16px;"><button
                                                            class="custom-button text  "><svg
                                                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                                width="1em" height="1em" fill="currentColor"
                                                                class="icon icon-play "
                                                                style="font-size: 24px; color: white;">
                                                                <path d="M8 5v14l11-7z"></path>
                                                            </svg></button><button class="custom-button text  "><svg
                                                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                                width="1em" height="1em" fill="currentColor"
                                                                class="icon icon-sound " style="color: white;">
                                                                <path
                                                                    d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z">
                                                                </path>
                                                            </svg></button><input type="range" min="0" max="1"
                                                            step="0.01" class="custom-slider " value="0.8"><span
                                                            class="player-controls-time">00:00 / 0:00</span></div>
                                                    <div
                                                        style="display: flex; align-items: center; gap: 16px; position: relative;">
                                                        <button class="custom-button text  "><svg
                                                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                                width="1em" height="1em" fill="currentColor"
                                                                class="icon icon-captions " style="color: white;">
                                                                <path
                                                                    d="M19 4H5c-1.11 0-2 .9-2 2v12c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm-8 7H9.5v-.5h-2v3h2V13H11v1c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1h3c.55 0 1 .45 1 1v1zm7 0h-1.5v-.5h-2v3h2V13H18v1c0 .55-.45 1-1 1h-3c-.55 0-1-.45-1-1v-4c0-.55.45-1 1-1h3c.55 0 1 .45 1 1v1z">
                                                                </path>
                                                            </svg></button><button
                                                            class="custom-button text  ">1x</button><button
                                                            class="custom-button text  "><svg
                                                                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                                width="1em" height="1em" fill="currentColor"
                                                                class="icon icon-fullscreen " style="color: white;">
                                                                <path
                                                                    d="M7 14H5v5h5v-2H7v-3zm-2-4h2V7h3V5H5v5zm12 7h-3v2h5v-5h-2v3zM14 5v2h3v3h2V5h-5z">
                                                                </path>
                                                            </svg></button></div>
                                                </div>
                                            </div>
                                            <div
                                                style="position: absolute; top: 20px; right: 20px; display: flex; flex-direction: column; gap: 10px;">
                                                <button class="custom-button primary circle "><svg
                                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                        width="1em" height="1em" fill="currentColor"
                                                        class="icon icon-heart ">
                                                        <path
                                                            d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z">
                                                        </path>
                                                    </svg></button><button class="custom-button primary circle "><svg
                                                        xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
                                                        width="1em" height="1em" fill="currentColor"
                                                        class="icon icon-robot ">
                                                        <path
                                                            d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4v3c0 .55.45 1 1 1h6c.55 0 1-.45 1-1v-3h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zM8 14c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm8 0c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zM15 9H9V7h6v2z">
                                                        </path>
                                                    </svg></button><button
                                                    class="custom-button primary circle ">🔄</button></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->


</body>

</html>