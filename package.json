{"name": "isia_frontend_web", "version": "0.1.0", "private": true, "dependencies": {"@mux/mux-player-react": "^3.5.1", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "media-chrome": "^4.12.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-player": "^3.1.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1", "screenfull": "^6.0.2", "styled-components": "^6.1.19", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}