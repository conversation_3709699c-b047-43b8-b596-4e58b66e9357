# 侧边导航栏展开收起功能 & 滚动问题修复

## 功能概述

在左侧导航栏最上方SmartQ的左侧添加了一个可展开收起左侧导航栏的按钮，并修复了导航栏滚动显示不完整的问题。

## 修改的文件

### 1. `src/components/layout/MainLayout.js`
- 添加了 `toggleCollapsed` 函数来处理侧边栏的展开收起状态
- 将 `onToggleCollapsed` 回调函数传递给 `SideNav` 组件

### 2. `src/components/layout/SideNav.js`
- 导入了 `Button` 组件
- 在 `sidenav-logo` 区域的最左侧添加了切换按钮
- 按钮使用菜单图标（三条横线）
- 接收 `onToggleCollapsed` 属性并在点击时调用

### 3. `src/assets/App.css`
- 修改了 `.sidenav-logo` 样式，将对齐方式从 `center` 改为 `flex-start`
- 添加了 `padding` 和 `gap` 来改善布局
- 添加了 `.sidenav-toggle-btn` 样式类
- 添加了收起状态下的样式规则

## 功能特性

1. **切换按钮位置**: 位于BrandName左侧，使用菜单图标
2. **响应式设计**: 
   - 展开状态：侧边栏宽度220px，显示BrandName文本
   - 收起状态：侧边栏宽度80px，隐藏BrandName文本，按钮居中显示
3. **平滑过渡**: 使用CSS transition实现宽度变化的平滑动画
4. **状态管理**: 通过React state管理展开收起状态

## 使用方法

用户只需点击侧边导航栏顶部的菜单按钮（三条横线图标）即可切换侧边栏的展开收起状态。

## 测试

创建了完整的单元测试来验证功能：

### `src/components/layout/__tests__/SideNav.test.js`
- 测试切换按钮的渲染
- 测试点击按钮时回调函数的调用
- 测试收起状态下BrandName的隐藏
- 测试展开状态下BrandName的显示
- 测试CSS类的正确应用

### `src/components/layout/__tests__/MainLayout.test.js`
- 测试SideNav组件的集成
- 测试切换功能的整体工作流程

所有测试都通过，确保功能正常工作且不会破坏现有功能。

## 滚动问题修复

### 问题描述
在打开新页签后，左侧导航栏下方显示不完整，无法通过鼠标滚动查看完整的导航栏内容。

### 解决方案
修改了CSS样式以确保侧边导航栏能够正确滚动：

1. **设置固定高度**: 为 `.sidenav` 添加 `height: 100vh` 和 `overflow: hidden`
2. **启用导航区域滚动**: 为 `.sidenav nav` 设置 `overflow-y: auto` 和 `min-height: 0`
3. **自定义滚动条样式**: 添加了美观的滚动条样式，包括细滚动条和悬停效果

### 修改的CSS样式
```css
.sidenav {
  height: 100vh;
  overflow: hidden;
}

.sidenav nav {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 0;
  scrollbar-width: thin;
  scrollbar-color: var(--border-color) transparent;
}
```

## 技术实现细节

1. **状态管理**: 使用React的 `useState` hook管理 `collapsed` 状态
2. **组件通信**: 通过props传递回调函数实现父子组件通信
3. **样式控制**: 通过条件CSS类和内联样式控制布局
4. **图标系统**: 使用现有的Icon组件系统，复用菜单图标
5. **按钮组件**: 使用现有的Button组件，配置为text类型以获得合适的样式
